# 联系人和分组唯一性验证功能

## 功能概述

本次更新为通讯录系统添加了两个重要的唯一性验证功能：

1. **联系人手机号码唯一性检查** - 确保同一用户下不能添加重复的手机号码
2. **通讯录分组名称唯一性检查** - 确保同一用户下同一父分组内不能有重复的分组名称

## 实现详情

### 1. 联系人手机号码唯一性检查

**文件位置**: `server/internal/logic/sys/contact.go`

**实现方式**:
- 在 `sSysContact.Edit()` 方法中添加了 `checkMobileUnique()` 调用
- 新增 `checkMobileUnique()` 方法实现具体的唯一性检查逻辑

**检查规则**:
- 在同一用户（member_id）范围内检查手机号码唯一性
- 新增联系人时：检查该手机号码是否已存在
- 修改联系人时：排除当前记录，检查其他记录是否有相同手机号码
- 如果发现重复，返回错误信息："手机号码已存在，请使用其他手机号码"

**代码示例**:
```go
// 验证手机号唯一性
if err = s.checkMobileUnique(ctx, in, memberId); err != nil {
    return
}
```

### 2. 通讯录分组名称唯一性检查

**文件位置**: `server/internal/logic/sys/contact_group.go`

**实现方式**:
- 在 `sSysContactGroup.Edit()` 方法中添加了 `checkNameUnique()` 调用
- 新增 `checkNameUnique()` 方法实现具体的唯一性检查逻辑

**检查规则**:
- 在同一用户（member_id）和同一父分组（pid）范围内检查分组名称唯一性
- 新增分组时：检查该分组名称在同一父分组下是否已存在
- 修改分组时：排除当前记录，检查其他记录是否有相同分组名称
- 如果发现重复，返回错误信息："分组名称已存在，请使用其他名称"

**代码示例**:
```go
// 验证分组名称唯一性
if err = s.checkNameUnique(ctx, in, memberId); err != nil {
    return
}
```

## 技术实现细节

### 实现位置说明
唯一性检查被放置在业务逻辑层（logic层）而不是输入验证层（input层），这是因为：
1. 输入验证层的 `Filter` 方法在中间件的 `PreFilter` 阶段执行，此时用户信息还未设置到上下文中
2. 业务逻辑层的 `Edit` 方法在用户认证中间件之后执行，可以正确获取用户信息
3. 这样的设计更符合分层架构的原则，业务验证放在业务逻辑层

### 数据库查询逻辑
两个检查方法都使用了类似的查询模式：
1. 接收用户ID作为参数（从业务逻辑层传入）
2. 构建查询条件（用户ID + 要检查的字段）
3. 如果是修改操作，排除当前记录
4. 执行计数查询
5. 如果计数大于0，返回错误

### 错误处理
- 数据库查询失败时包装错误信息
- 发现重复数据时返回用户友好的错误信息
- 错误信息会被自动传递到前端显示给用户

## 使用说明

### 前端调用
前端在调用联系人或分组的新增/修改接口时，如果违反唯一性约束，会收到相应的错误信息，可以直接展示给用户。

### 测试建议
1. 测试新增联系人时使用已存在的手机号码
2. 测试修改联系人时改为已存在的手机号码
3. 测试在同一父分组下新增重复名称的分组
4. 测试修改分组名称为同一父分组下已存在的名称

## 注意事项

1. **用户隔离**: 所有检查都在用户级别进行，不同用户之间的数据不会相互影响
2. **分组层级**: 分组名称唯一性检查是在同一父分组层级下进行的，不同父分组下可以有相同名称的子分组
3. **性能考虑**: 唯一性检查会增加一次数据库查询，但这是必要的数据完整性保证
4. **事务处理**: 建议在实际的新增/修改操作中使用数据库事务，确保数据一致性

## 后续优化建议

1. 可以考虑在数据库层面添加唯一索引，提供双重保障
2. 可以实现批量导入时的唯一性检查
3. 可以添加更详细的错误信息，指出具体冲突的记录
