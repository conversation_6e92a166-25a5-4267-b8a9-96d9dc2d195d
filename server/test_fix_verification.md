# 唯一性验证修复验证

## 问题描述
之前在新增和编辑联系人时，后台返回错误：
```json
{
    "code": -1,
    "message": "用户信息获取失败",
    "timestamp": 1751700802,
    "traceID": "1caed659994a4f1898e80165a870af3e"
}
```

## 问题原因
唯一性检查代码最初放在了输入验证层（input层）的 `Filter` 方法中，但该方法在中间件的 `PreFilter` 阶段执行，此时用户信息还未设置到上下文中。

中间件执行顺序：
1. `Ctx` - 初始化请求上下文
2. `CORS` - 跨域处理
3. `Blacklist` - IP黑名单
4. `DemoLimit` - 演示限制
5. `PreFilter` - 请求输入预处理（这里会调用 Filter 方法）
6. `ResponseHandler` - 响应处理
7. `AdminAuth` - 用户认证（这里才设置用户信息到上下文）

## 解决方案
将唯一性检查从输入验证层移动到业务逻辑层：

### 修改前
```go
// 在 ContactEditInp.Filter() 中
if err := in.checkMobileUnique(ctx); err != nil {
    return err
}
```

### 修改后
```go
// 在 sSysContact.Edit() 中
if err = s.checkMobileUnique(ctx, in, memberId); err != nil {
    return
}
```

## 修改的文件

### 1. server/internal/model/input/adminin/contact.go
- 移除了 `checkMobileUnique` 方法
- 清理了不再需要的导入

### 2. server/internal/model/input/adminin/contact_group.go
- 移除了 `checkNameUnique` 方法
- 清理了不再需要的导入

### 3. server/internal/logic/sys/contact.go
- 在 `Edit` 方法中添加了唯一性检查调用
- 新增了 `checkMobileUnique` 方法

### 4. server/internal/logic/sys/contact_group.go
- 在 `Edit` 方法中添加了唯一性检查调用
- 新增了 `checkNameUnique` 方法

## 验证步骤

1. **编译检查**: ✅ 已通过
2. **功能测试**:
   - 新增联系人时使用重复手机号码
   - 修改联系人时改为重复手机号码
   - 新增分组时使用重复名称
   - 修改分组时改为重复名称

## 预期结果
- 不再出现"用户信息获取失败"错误
- 唯一性验证正常工作
- 重复数据时返回友好的错误信息

## 技术优势
1. **正确的分层**: 业务验证放在业务逻辑层
2. **用户隔离**: 确保在正确的用户上下文中进行验证
3. **错误处理**: 提供清晰的错误信息
4. **性能**: 避免不必要的数据库查询
