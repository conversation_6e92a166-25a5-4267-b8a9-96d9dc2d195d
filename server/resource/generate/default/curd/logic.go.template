// Package @{.templateGroup}
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) @{NowYear} HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version @{.hgVersion}
//
package @{.templateGroup}

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/internal/dao"
	"hotgo/internal/consts"
	"hotgo/internal/library/contexts"
	"hotgo/internal/library/hgorm"
	"hotgo/internal/library/hgorm/handler"
	"hotgo/internal/library/hgorm/hook"
	"@{.importInput}"
	"hotgo/internal/model/input/form"
	"@{.importService}"
	"hotgo/utility/convert"
	"hotgo/utility/excel"
	"hotgo/utility/validate"
	@{ if eq .options.Step.IsAddon true }isc "hotgo/internal/service"@{end}
	@{ if eq .options.Step.IsTreeTable true }"hotgo/utility/tree"@{end}
	@{ if eq .options.Step.HasFuncDict true }"hotgo/internal/library/dict"@{end}
	@{ if eq .options.Step.HasFuncDict true }"hotgo/internal/model"@{end}
)

type s@{.servFunName} struct{}

func New@{.servFunName}() *s@{.servFunName} {
	return &s@{.servFunName}{}
}

func init() {
	service.Register@{.servFunName}(New@{.servFunName}())
	@{ if eq .options.Step.HasFuncDict true }dict.RegisterFunc("@{.varName | LcFirst}Option", "@{.tableComment}选项", service.@{.servFunName}().Option)@{end}}

// Model @{.tableComment}ORM模型
func (s *s@{.servFunName}) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.@{.daoName}.Ctx(ctx), option...)
}

// List 获取@{.tableComment}列表
func (s *s@{.servFunName}) List(ctx context.Context, in *@{.templateGroup}in.@{.varName}ListInp) (list []*@{.templateGroup}in.@{.varName}ListModel, totalCount int, err error) {
	mod := s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end})

	// 字段过滤
  @{.listFields}

	@{ if ne .listJoin "" }
  // 关联表字段
  @{.listJoin}
  @{end}

@{.listWhere}

@{ if eq .options.Step.IsTreeTable true }
  // 树形列表判断是否需要分页
	if in.Pagination {
		mod = mod.Page(in.Page, in.PerPage)
	}
@{end}

@{ if eq .options.Step.IsTreeTable false }
  // 分页
	mod = mod.Page(in.Page, in.PerPage)
@{end}

	// 排序
	mod = mod.@{.listOrder}

@{ if eq .options.Step.HasHookMemberSummary true }
	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)
@{end}

	// 查询数据
    if err = mod.ScanAndCount(&list, &totalCount, false);err != nil {
		err = gerror.Wrap(err, "获取@{.tableComment}列表失败，请稍后重试！")
		return
    }
	return
}

@{ if eq .options.Step.HasExport true }
// Export 导出@{.tableComment}
func (s *s@{.servFunName}) Export(ctx context.Context, in *@{.templateGroup}in.@{.varName}ListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(@{.templateGroup}in.@{.varName}ExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出@{.tableComment}-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []@{.templateGroup}in.@{.varName}ExportModel
	)

	if err = gconv.Scan(list, &exports);err != nil {
		return
	}

    err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}@{end}

@{ if eq .options.Step.HasEdit true }
// Edit 修改/新增@{.tableComment}
func (s *s@{.servFunName}) Edit(ctx context.Context, in *@{.templateGroup}in.@{.varName}EditInp) (err error) {
@{.edit.unique}	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		@{ if eq .options.Step.IsTreeTable true }in.Pid, in.Level, in.Tree, err = hgorm.AutoUpdateTree(ctx, &dao.@{.daoName}, in.@{.pk.GoName}, in.Pid)
		if err != nil {
			return err
		}@{end}
		// 修改
		if in.@{.pk.GoName} > 0 {
	@{.edit.update}
		}

		// 新增
	@{.edit.insert}
		return
	})
}
@{end}

@{ if eq .options.Step.HasDel true }
// Delete 删除@{.tableComment}
func (s *s@{.servFunName}) Delete(ctx context.Context, in *@{.templateGroup}in.@{.varName}DeleteInp) (err error) {
	@{ if eq .options.Step.IsTreeTable true }count, err := dao.@{.daoName}.Ctx(ctx).Where(dao.@{.daoName}.Columns().Pid, in.@{.pk.GoName}).Count()
	if err != nil {
		err = gerror.Wrap(err, consts.ErrorORM)
		return err
	}
	if count > 0 {
		return gerror.New("请先删除该@{.tableComment}下的所有下级！")
	}@{end}
	@{ if eq .deletedUpdate "" }
	if _, err = s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end}).WherePri(in.@{.pk.GoName}).Unscoped().Delete();err != nil {
		err = gerror.Wrap(err, "删除@{.tableComment}失败，请稍后重试！")
    	return
	}@{else}
	if _, err = s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end}).WherePri(in.@{.pk.GoName}).Data(@{.deletedUpdate}).Unscoped().Update();err != nil {
		err = gerror.Wrap(err, "删除@{.tableComment}失败，请稍后重试！")
		return
	}@{end}
	return
}@{end}

@{ if and (eq .options.Step.HasEdit true) (eq .options.Step.HasMaxSort true) }
// MaxSort 获取@{.tableComment}最大排序
func (s *s@{.servFunName}) MaxSort(ctx context.Context, in *@{.templateGroup}in.@{.varName}MaxSortInp) (res *@{.templateGroup}in.@{.varName}MaxSortModel, err error) {
	if err = dao.@{.daoName}.Ctx(ctx).Fields(dao.@{.daoName}.Columns().Sort).OrderDesc(dao.@{.daoName}.Columns().Sort).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取@{.tableComment}最大排序，请稍后重试！")
		return
	}

	if res == nil {
		res = new(@{.templateGroup}in.@{.varName}MaxSortModel)
	}

	res.Sort = form.DefaultMaxSort(res.Sort)
	return
}
@{end}

@{ if or (eq .options.Step.HasView true) (eq .options.Step.HasEdit true) }
// View 获取@{.tableComment}指定信息
func (s *s@{.servFunName}) View(ctx context.Context, in *@{.templateGroup}in.@{.varName}ViewInp) (res *@{.templateGroup}in.@{.varName}ViewModel, err error) {
    if err = s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end}).WherePri(in.@{.pk.GoName})@{ if eq .options.Step.HasHookMemberSummary true }.Hook(hook.MemberSummary)@{end}.Scan(&res);err != nil {
		err = gerror.Wrap(err, "获取@{.tableComment}信息，请稍后重试！")
		return
    }
	return
}@{end}

@{ if eq .options.Step.HasStatus true }
// Status 更新@{.tableComment}状态
func (s *s@{.servFunName}) Status(ctx context.Context, in *@{.templateGroup}in.@{.varName}StatusInp) (err error) {
	if _, err = s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end}).WherePri(in.@{.pk.GoName}).Data(@{.statusUpdate}).Update();err != nil {
		err = gerror.Wrap(err, "更新@{.tableComment}状态失败，请稍后重试！")
		return
	}
	return
}
@{end}

@{ if eq .options.Step.HasSwitch true }
// Switch 更新@{.tableComment}开关
func (s *s@{.servFunName}) Switch(ctx context.Context, in *@{.templateGroup}in.@{.varName}SwitchInp) (err error) {
	var fields = []string{
@{.switchFields}		// ...
	}

	if !validate.InSlice(fields, in.Key) {
		err = gerror.New("开关键名不在白名单")
		return
	}

	if _, err = s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end}).WherePri(in.@{.pk.GoName}).Data(@{.switchUpdate}).Update();err != nil {
		err = gerror.Wrap(err, "更新@{.tableComment}开关失败，请稍后重试！")
		return
	}
	return
}
@{end}

@{ if eq .options.Step.IsTreeTable true }
// TreeOption 获取@{.tableComment}关系树选项
func (s *s@{.servFunName}) TreeOption(ctx context.Context) (nodes []tree.Node, err error) {
	var models []*@{.templateGroup}in.@{.varName}TreeOption
	if err = s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end}).Fields(@{.templateGroup}in.@{.varName}TreeOption{}).OrderAsc(dao.@{.daoName}.Columns().Pid).@{.listOrder}.Scan(&models); err != nil {
		err = gerror.Wrap(err, "获取@{.tableComment}关系树选项失败！")
		return
	}
	nodes = make([]tree.Node, len(models))
	for i, v := range models {
		nodes[i] = v
	}
	return tree.ListToTree(0, nodes)
}
@{end}

@{ if eq .options.Step.HasFuncDict true }
// Option 获取@{.tableComment}选项
func (s *s@{.servFunName}) Option(ctx context.Context) (opts []*model.Option, err error) {
	var models []*entity.@{.daoName}
	if err = s.Model(ctx@{ if eq .options.Step.HasNotFilterAuth true } ,&handler.Option{FilterAuth: false}@{end}).Fields(dao.@{.daoName}.Columns().@{.options.FuncDict.Value.GoName},dao.@{.daoName}.Columns().@{.options.FuncDict.Label.GoName}).
	@{.listOrder}.Scan(&models); err != nil {
		err = gerror.Wrap(err, "获取@{.tableComment}选项失败！")
		return
	}

	opts = make([]*model.Option, len(models))
	for k, v := range models {
		opts[k] = dict.GenHashOption(v.@{.options.FuncDict.Value.GoName}, gconv.String(v.@{.options.FuncDict.Label.GoName}))
	}
	return
}
@{end}