// Package @{.templateGroup}in
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) @{NowYear} HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version @{.hgVersion}
//
package @{.templateGroup}in

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"hotgo/internal/consts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	"hotgo/utility/validate"
	@{ if eq .options.Step.HasHookMemberSummary true }"hotgo/internal/library/hgorm/hook"@{end}
	@{ if eq .options.Step.IsTreeTable true }"hotgo/utility/tree"@{end}
)
@{ if eq .options.Step.HasEdit true }
// @{.varName}UpdateFields 修改@{.tableComment}字段过滤
type @{.varName}UpdateFields struct {
@{.updateFieldsColumns}
}

// @{.varName}InsertFields 新增@{.tableComment}字段过滤
type @{.varName}InsertFields struct {
@{.insertFieldsColumns}
}

// @{.varName}EditInp 修改/新增@{.tableComment}
type @{.varName}EditInp struct {
	entity.@{.daoName}
}

func (in *@{.varName}EditInp) Filter(ctx context.Context) (err error) {
@{.editInpValidator}
	return
}

type @{.varName}EditModel struct{}
@{end}
@{ if eq .options.Step.HasDel true }
// @{.varName}DeleteInp 删除@{.tableComment}
type @{.varName}DeleteInp struct {
	@{.pk.GoName} interface{} `json:"@{.pk.TsName}" v:"required#@{.pk.Dc}不能为空" dc:"@{.pk.Dc}"`
}

func (in *@{.varName}DeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type @{.varName}DeleteModel struct{}
@{end}
@{ if or (eq .options.Step.HasView true) (eq .options.Step.HasEdit true) }
// @{.varName}ViewInp 获取指定@{.tableComment}信息
type @{.varName}ViewInp struct {
	@{.pk.GoName} @{.pk.GoType} `json:"@{.pk.TsName}" v:"required#@{.pk.Dc}不能为空" dc:"@{.pk.Dc}"`
}

func (in *@{.varName}ViewInp) Filter(ctx context.Context) (err error) {
	return
}

type @{.varName}ViewModel struct {
@{.viewModelColumns}
}@{end}

// @{.varName}ListInp 获取@{.tableComment}列表
type @{.varName}ListInp struct {
	form.PageReq
@{.listInpColumns}
}

func (in *@{.varName}ListInp) Filter(ctx context.Context) (err error) {
	return
}

type @{.varName}ListModel struct {
@{.listModelColumns}
}

@{ if eq .options.Step.HasExport true }
// @{.varName}ExportModel 导出@{.tableComment}
type @{.varName}ExportModel struct {
@{.exportModelColumns}
}@{end}
@{ if and (eq .options.Step.HasEdit true) (eq .options.Step.HasMaxSort true) }
// @{.varName}MaxSortInp 获取@{.tableComment}最大排序
type @{.varName}MaxSortInp struct{}

func (in *@{.varName}MaxSortInp) Filter(ctx context.Context) (err error) {
	return
}

type @{.varName}MaxSortModel struct {
	Sort int `json:"sort"  description:"排序"`
}
@{end}
@{ if eq .options.Step.HasStatus true }
// @{.varName}StatusInp 更新@{.tableComment}状态
type @{.varName}StatusInp struct {
	@{.pk.GoName} @{.pk.GoType} `json:"@{.pk.TsName}" v:"required#@{.pk.Dc}不能为空" dc:"@{.pk.Dc}"`
	Status int   `json:"status" dc:"状态"`
}

func (in *@{.varName}StatusInp) Filter(ctx context.Context) (err error) {
	if in.@{.pk.GoName} <= 0 {
		err = gerror.New("@{.pk.Dc}不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type @{.varName}StatusModel struct{}
@{end}
@{ if eq .options.Step.HasSwitch true }
// @{.varName}SwitchInp 更新@{.tableComment}开关状态
type @{.varName}SwitchInp struct {
	form.SwitchReq
	@{.pk.GoName} @{.pk.GoType} `json:"@{.pk.TsName}" v:"required#@{.pk.Dc}不能为空" dc:"@{.pk.Dc}"`
}

func (in *@{.varName}SwitchInp) Filter(ctx context.Context) (err error) {
	return
}

type @{.varName}SwitchModel struct{}
@{end}

@{ if eq .options.Step.IsTreeTable true }
// @{.varName}TreeOption 关系树选项
type @{.varName}TreeOption struct {
@{.treeOptionFields}
	Children []tree.Node `json:"children"  dc:"子节点"`
}

// ID 获取节点ID
func (t *@{.varName}TreeOption) ID() int64 {
	return t.@{.pk.GoName}
}

// PID 获取父级节点ID
func (t *@{.varName}TreeOption) PID() int64 {
	return t.Pid
}

// SetChildren 设置子节点数据
func (t *@{.varName}TreeOption) SetChildren(children []tree.Node) {
	t.Children = children
}
@{end}

