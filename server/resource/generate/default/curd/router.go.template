// Package genrouter
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) @{NowYear} HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version @{.hgVersion}
//
package genrouter

import "@{.importController}"

func init() {
	LoginRequiredRouter = append(LoginRequiredRouter, @{.templateGroup}.@{.varName}) // @{.tableComment}
}
