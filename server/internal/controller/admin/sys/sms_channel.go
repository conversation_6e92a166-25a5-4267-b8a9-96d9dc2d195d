// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/smschannel"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	SmsChannel = cSmsChannel{}
)

type cSmsChannel struct{}

// List 查看短信通道列表
func (c *cSmsChannel) List(ctx context.Context, req *smschannel.ListReq) (res *smschannel.ListRes, err error) {
	list, totalCount, err := service.SysSmsChannel().List(ctx, &req.SmsChannelListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.SmsChannelListModel{}
	}

	res = new(smschannel.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出短信通道列表
func (c *cSmsChannel) Export(ctx context.Context, req *smschannel.ExportReq) (res *smschannel.ExportRes, err error) {
	err = service.SysSmsChannel().Export(ctx, &req.SmsChannelListInp)
	return
}

// Edit 更新短信通道
func (c *cSmsChannel) Edit(ctx context.Context, req *smschannel.EditReq) (res *smschannel.EditRes, err error) {
	err = service.SysSmsChannel().Edit(ctx, &req.SmsChannelEditInp)
	return
}

// MaxSort 获取短信通道最大排序
func (c *cSmsChannel) MaxSort(ctx context.Context, req *smschannel.MaxSortReq) (res *smschannel.MaxSortRes, err error) {
	data, err := service.SysSmsChannel().MaxSort(ctx, &req.SmsChannelMaxSortInp)
	if err != nil {
		return
	}

	res = new(smschannel.MaxSortRes)
	res.SmsChannelMaxSortModel = data
	return
}

// View 获取指定短信通道信息
func (c *cSmsChannel) View(ctx context.Context, req *smschannel.ViewReq) (res *smschannel.ViewRes, err error) {
	data, err := service.SysSmsChannel().View(ctx, &req.SmsChannelViewInp)
	if err != nil {
		return
	}

	res = new(smschannel.ViewRes)
	res.SmsChannelViewModel = data
	return
}

// Delete 删除短信通道
func (c *cSmsChannel) Delete(ctx context.Context, req *smschannel.DeleteReq) (res *smschannel.DeleteRes, err error) {
	err = service.SysSmsChannel().Delete(ctx, &req.SmsChannelDeleteInp)
	return
}

// Status 更新短信通道状态
func (c *cSmsChannel) Status(ctx context.Context, req *smschannel.StatusReq) (res *smschannel.StatusRes, err error) {
	err = service.SysSmsChannel().Status(ctx, &req.SmsChannelStatusInp)
	return
}

// TreeOption 获取短信通道关系树选项
func (c *cSmsChannel) TreeOption(ctx context.Context, req *smschannel.TreeOptionReq) (res *smschannel.TreeOptionRes, err error) {
	data, err := service.SysSmsChannel().TreeOption(ctx)
	if err != nil {
		return nil, err
	}

	if len(data) > 0 {
		res = (*smschannel.TreeOptionRes)(&data)
	} else {
		temp := make(smschannel.TreeOptionRes, 0)
		res = &temp
	}
	return
}
