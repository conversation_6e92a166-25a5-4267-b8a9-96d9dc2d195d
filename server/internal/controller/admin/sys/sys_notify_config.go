// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/sysnotifyconfig"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	SysNotifyConfig = cSysNotifyConfig{}
)

type cSysNotifyConfig struct{}

// List 查看系统_审核通知配置列表
func (c *cSysNotifyConfig) List(ctx context.Context, req *sysnotifyconfig.ListReq) (res *sysnotifyconfig.ListRes, err error) {
	list, totalCount, err := service.SysNotifyConfig().List(ctx, &req.SysNotifyConfigListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.SysNotifyConfigListModel{}
	}

	res = new(sysnotifyconfig.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出系统_审核通知配置列表
func (c *cSysNotifyConfig) Export(ctx context.Context, req *sysnotifyconfig.ExportReq) (res *sysnotifyconfig.ExportRes, err error) {
	err = service.SysNotifyConfig().Export(ctx, &req.SysNotifyConfigListInp)
	return
}

// Edit 更新系统_审核通知配置
func (c *cSysNotifyConfig) Edit(ctx context.Context, req *sysnotifyconfig.EditReq) (res *sysnotifyconfig.EditRes, err error) {
	err = service.SysNotifyConfig().Edit(ctx, &req.SysNotifyConfigEditInp)
	return
}

// View 获取指定系统_审核通知配置信息
func (c *cSysNotifyConfig) View(ctx context.Context, req *sysnotifyconfig.ViewReq) (res *sysnotifyconfig.ViewRes, err error) {
	data, err := service.SysNotifyConfig().View(ctx, &req.SysNotifyConfigViewInp)
	if err != nil {
		return
	}

	res = new(sysnotifyconfig.ViewRes)
	res.SysNotifyConfigViewModel = data
	return
}

// Delete 删除系统_审核通知配置
func (c *cSysNotifyConfig) Delete(ctx context.Context, req *sysnotifyconfig.DeleteReq) (res *sysnotifyconfig.DeleteRes, err error) {
	err = service.SysNotifyConfig().Delete(ctx, &req.SysNotifyConfigDeleteInp)
	return
}

// Status 更新系统_审核通知配置状态
func (c *cSysNotifyConfig) Status(ctx context.Context, req *sysnotifyconfig.StatusReq) (res *sysnotifyconfig.StatusRes, err error) {
	err = service.SysNotifyConfig().Status(ctx, &req.SysNotifyConfigStatusInp)
	return
}