// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/admincompany"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	AdminCompany = cAdminCompany{}
)

type cAdminCompany struct{}

// List 查看企业列表列表
func (c *cAdminCompany) List(ctx context.Context, req *admincompany.ListReq) (res *admincompany.ListRes, err error) {
	list, totalCount, err := service.SysAdminCompany().List(ctx, &req.AdminCompanyListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.AdminCompanyListModel{}
	}

	res = new(admincompany.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出企业列表列表
func (c *cAdminCompany) Export(ctx context.Context, req *admincompany.ExportReq) (res *admincompany.ExportRes, err error) {
	err = service.SysAdminCompany().Export(ctx, &req.AdminCompanyListInp)
	return
}

// Edit 更新企业列表
func (c *cAdminCompany) Edit(ctx context.Context, req *admincompany.EditReq) (res *admincompany.EditRes, err error) {
	err = service.SysAdminCompany().Edit(ctx, &req.AdminCompanyEditInp)
	return
}

// MaxSort 获取企业列表最大排序
func (c *cAdminCompany) MaxSort(ctx context.Context, req *admincompany.MaxSortReq) (res *admincompany.MaxSortRes, err error) {
	data, err := service.SysAdminCompany().MaxSort(ctx, &req.AdminCompanyMaxSortInp)
	if err != nil {
		return
	}

	res = new(admincompany.MaxSortRes)
	res.AdminCompanyMaxSortModel = data
	return
}

// View 获取指定企业列表信息
func (c *cAdminCompany) View(ctx context.Context, req *admincompany.ViewReq) (res *admincompany.ViewRes, err error) {
	data, err := service.SysAdminCompany().View(ctx, &req.AdminCompanyViewInp)
	if err != nil {
		return
	}

	res = new(admincompany.ViewRes)
	res.AdminCompanyViewModel = data
	return
}

// Delete 删除企业列表
func (c *cAdminCompany) Delete(ctx context.Context, req *admincompany.DeleteReq) (res *admincompany.DeleteRes, err error) {
	err = service.SysAdminCompany().Delete(ctx, &req.AdminCompanyDeleteInp)
	return
}

// TreeOption 获取企业列表关系树选项
func (c *cAdminCompany) TreeOption(ctx context.Context, req *admincompany.TreeOptionReq) (res *admincompany.TreeOptionRes, err error) {
	data, err := service.SysAdminCompany().TreeOption(ctx)
	if err != nil {
		return nil, err
	}

	if len(data) > 0 {
		res = (*admincompany.TreeOptionRes)(&data)
	} else {
		temp := make(admincompany.TreeOptionRes, 0)
		res = &temp
	}
	return
}

// Status 更新短信签名状态
func (c *cAdminCompany) Status(ctx context.Context, req *admincompany.StatusReq) (res *admincompany.StatusRes, err error) {
	err = service.SysAdminCompany().Status(ctx, &req.AdminCompanyStatusInp)
	return
}

// AddBalance 变更余额
func (c *cAdminCompany) AddBalance(ctx context.Context, req *admincompany.AddBalanceReq) (res *admincompany.AddBalanceRes, err error) {
	err = service.SysAdminCompany().AddBalance(ctx, &req.AdminCompanyAddBalanceInp)
	return
}

// AddSmsBalance 短信充值
func (c *cAdminCompany) AddSmsBalance(ctx context.Context, req *admincompany.AddSmsBalanceReq) (res *admincompany.AddSmsBalanceRes, err error) {
	err = service.SysAdminCompany().AddSmsBalance(ctx, &req.AdminCompanyAddSMSBalanceInp)
	return
}

// Workbench 工作台
func (c *cAdminCompany) Workbench(ctx context.Context, req *admincompany.WorkbenchReq) (res *admincompany.WorkbenchRes, err error) {
	data, err := service.SysAdminCompany().Workbench(ctx, &req.AdminCompanyWorkbenchInp)
	if err != nil {
		return
	}

	res = new(admincompany.WorkbenchRes)
	res.AdminCompanyWorkbenchModel = data
	return
}

// CompanyTree 获取企业树形结构
func (c *cAdminCompany) CompanyTree(ctx context.Context, req *admincompany.CompanyTreeReq) (res *admincompany.CompanyTreeRes, err error) {
	data, err := service.SysAdminCompany().CompanyTree(ctx, &sysin.CompanyTreeInp{})
	if err != nil {
		return nil, err
	}
	return (*admincompany.CompanyTreeRes)(&data), nil
}
