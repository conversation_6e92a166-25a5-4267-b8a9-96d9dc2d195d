// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/contactgroup"
	"hotgo/internal/model/input/form"
	"hotgo/internal/service"
)

var ContactGroup = cContactGroup{}

type cContactGroup struct{}

// List 查看通讯录分组列表
func (c *cContactGroup) List(ctx context.Context, req *contactgroup.ListReq) (res *contactgroup.ListRes, err error) {
	list, totalCount, err := service.SysContactGroup().List(ctx, &req.ContactGroupListInp)
	if err != nil {
		return
	}

	res = &contactgroup.ListRes{
		List:                list,
		Total:               totalCount,
		TotalCount:          totalCount,
		PageCount:           form.CalPageCount(totalCount, req.ContactGroupListInp.PerPage),
		ContactGroupListInp: &req.ContactGroupListInp,
	}
	return
}

// Edit 修改/新增通讯录分组
func (c *cContactGroup) Edit(ctx context.Context, req *contactgroup.EditReq) (res *contactgroup.EditRes, err error) {
	err = service.SysContactGroup().Edit(ctx, &req.ContactGroupEditInp)
	return
}

// View 获取指定通讯录分组信息
func (c *cContactGroup) View(ctx context.Context, req *contactgroup.ViewReq) (res *contactgroup.ViewRes, err error) {
	data, err := service.SysContactGroup().View(ctx, &req.ContactGroupViewInp)
	if err != nil {
		return
	}

	res = &contactgroup.ViewRes{ContactGroupViewModel: data}
	return
}

// Delete 删除通讯录分组
func (c *cContactGroup) Delete(ctx context.Context, req *contactgroup.DeleteReq) (res *contactgroup.DeleteRes, err error) {
	err = service.SysContactGroup().Delete(ctx, &req.ContactGroupDeleteInp)
	return
}

// Status 更新通讯录分组状态
func (c *cContactGroup) Status(ctx context.Context, req *contactgroup.StatusReq) (res *contactgroup.StatusRes, err error) {
	err = service.SysContactGroup().Status(ctx, &req.ContactGroupStatusInp)
	return
}

// TreeOption 获取通讯录分组关系树选项
func (c *cContactGroup) TreeOption(ctx context.Context, req *contactgroup.TreeOptionReq) (res contactgroup.TreeOptionRes, err error) {
	list, err := service.SysContactGroup().TreeOption(ctx)
	if err != nil {
		return
	}

	res = list
	return
}
