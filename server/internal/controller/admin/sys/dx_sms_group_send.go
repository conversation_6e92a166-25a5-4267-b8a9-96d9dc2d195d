// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmsgroupsend"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsGroupSend = cDxSmsGroupSend{}
)

type cDxSmsGroupSend struct{}

// List 查看短信群发列表
func (c *cDxSmsGroupSend) List(ctx context.Context, req *dxsmsgroupsend.ListReq) (res *dxsmsgroupsend.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsGroupSend().List(ctx, &req.DxSmsGroupSendListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsGroupSendListModel{}
	}

	res = new(dxsmsgroupsend.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Edit 更新短信群发
func (c *cDxSmsGroupSend) Edit(ctx context.Context, req *dxsmsgroupsend.EditReq) (res *dxsmsgroupsend.EditRes, err error) {
	err = service.SysDxSmsGroupSend().Edit(ctx, &req.DxSmsGroupSendEditInp)
	return
}

// View 获取指定短信群发信息
func (c *cDxSmsGroupSend) View(ctx context.Context, req *dxsmsgroupsend.ViewReq) (res *dxsmsgroupsend.ViewRes, err error) {
	data, err := service.SysDxSmsGroupSend().View(ctx, &req.DxSmsGroupSendViewInp)
	if err != nil {
		return
	}

	res = new(dxsmsgroupsend.ViewRes)
	res.DxSmsGroupSendViewModel = data
	return
}

// Delete 删除短信群发
func (c *cDxSmsGroupSend) Delete(ctx context.Context, req *dxsmsgroupsend.DeleteReq) (res *dxsmsgroupsend.DeleteRes, err error) {
	err = service.SysDxSmsGroupSend().Delete(ctx, &req.DxSmsGroupSendDeleteInp)
	return
}

// CheckFile 检查短信群发文件
func (c *cDxSmsGroupSend) CheckFile(ctx context.Context, req *dxsmsgroupsend.CheckFileReq) (res *dxsmsgroupsend.CheckFileRes, err error) {
	data, err := service.SysDxSmsGroupSend().CheckFile(ctx, &sysin.DxSmsGroupSendCheckFileInp{
		AttachmentId: req.AttachmentId,
		FileUrl:      req.FileUrl,
		TemplateId:   req.TemplateId,
	})
	if err != nil {
		return nil, err
	}

	return &dxsmsgroupsend.CheckFileRes{
		SuccessCount:   data.SuccessCount,
		FailCount:      data.FailCount,
		DuplicateCount: data.DuplicateCount,
		NewFileUrl:     data.NewFileUrl,
	}, nil
}

// Cancel 取消发送
func (c *cDxSmsGroupSend) Cancel(ctx context.Context, req *dxsmsgroupsend.CancelReq) (res *dxsmsgroupsend.CancelRes, err error) {
	err = service.SysDxSmsGroupSend().Cancel(ctx, &req.DxSmsGroupSendCancelInp)
	return
}
