// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmsgrouptemplate"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsGroupTemplate = cDxSmsGroupTemplate{}
)

type cDxSmsGroupTemplate struct{}

// List 查看模板管理列表
func (c *cDxSmsGroupTemplate) List(ctx context.Context, req *dxsmsgrouptemplate.ListReq) (res *dxsmsgrouptemplate.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsGroupTemplate().List(ctx, &req.DxSmsGroupTemplateListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsGroupTemplateListModel{}
	}

	res = new(dxsmsgrouptemplate.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出模板管理列表
func (c *cDxSmsGroupTemplate) Export(ctx context.Context, req *dxsmsgrouptemplate.ExportReq) (res *dxsmsgrouptemplate.ExportRes, err error) {
	err = service.SysDxSmsGroupTemplate().Export(ctx, &req.DxSmsGroupTemplateListInp)
	return
}

// Edit 更新模板管理
func (c *cDxSmsGroupTemplate) Edit(ctx context.Context, req *dxsmsgrouptemplate.EditReq) (res *dxsmsgrouptemplate.EditRes, err error) {
	err = service.SysDxSmsGroupTemplate().Edit(ctx, &req.DxSmsGroupTemplateEditInp)
	return
}

// View 获取指定模板管理信息
func (c *cDxSmsGroupTemplate) View(ctx context.Context, req *dxsmsgrouptemplate.ViewReq) (res *dxsmsgrouptemplate.ViewRes, err error) {
	data, err := service.SysDxSmsGroupTemplate().View(ctx, &req.DxSmsGroupTemplateViewInp)
	if err != nil {
		return
	}

	res = new(dxsmsgrouptemplate.ViewRes)
	res.DxSmsGroupTemplateViewModel = data
	return
}

// Delete 删除模板管理
func (c *cDxSmsGroupTemplate) Delete(ctx context.Context, req *dxsmsgrouptemplate.DeleteReq) (res *dxsmsgrouptemplate.DeleteRes, err error) {
	err = service.SysDxSmsGroupTemplate().Delete(ctx, &req.DxSmsGroupTemplateDeleteInp)
	return
}

// Status 更新模板管理状态
func (c *cDxSmsGroupTemplate) Status(ctx context.Context, req *dxsmsgrouptemplate.StatusReq) (res *dxsmsgrouptemplate.StatusRes, err error) {
	err = service.SysDxSmsGroupTemplate().Status(ctx, &req.DxSmsGroupTemplateStatusInp)
	return
}

// Option 查看模板选项列表
func (c *cDxSmsGroupTemplate) Option(ctx context.Context, req *dxsmsgrouptemplate.OptionReq) (res *dxsmsgrouptemplate.OptionRes, err error) {
	list, err := service.SysDxSmsGroupTemplate().Option(ctx, &req.DxSmsGroupTemplateOptionInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsGroupTemplateOption{}
	}

	res = new(dxsmsgrouptemplate.OptionRes)
	res.List = list
	return
}

// DownloadExcel 下载Excel
func (c *cDxSmsGroupTemplate) DownloadExcel(ctx context.Context, req *dxsmsgrouptemplate.DownloadExcelReq) (res *dxsmsgrouptemplate.DownloadExcelRes, err error) {
	err = service.SysDxSmsGroupTemplate().DownloadExcel(ctx, &req.DxSmsGroupTemplateViewInp)
	return
}
