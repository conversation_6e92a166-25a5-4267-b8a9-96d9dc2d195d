// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmsmorecord"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsMoRecord = cDxSmsMoRecord{}
)

type cDxSmsMoRecord struct{}

// List 查看上行记录列表
func (c *cDxSmsMoRecord) List(ctx context.Context, req *dxsmsmorecord.ListReq) (res *dxsmsmorecord.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsMoRecord().List(ctx, &req.DxSmsMoRecordListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsMoRecordListModel{}
	}

	res = new(dxsmsmorecord.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出上行记录列表
func (c *cDxSmsMoRecord) Export(ctx context.Context, req *dxsmsmorecord.ExportReq) (res *dxsmsmorecord.ExportRes, err error) {
	err = service.SysDxSmsMoRecord().Export(ctx, &req.DxSmsMoRecordListInp)
	return
}

// View 获取指定上行记录信息
func (c *cDxSmsMoRecord) View(ctx context.Context, req *dxsmsmorecord.ViewReq) (res *dxsmsmorecord.ViewRes, err error) {
	data, err := service.SysDxSmsMoRecord().View(ctx, &req.DxSmsMoRecordViewInp)
	if err != nil {
		return
	}

	res = new(dxsmsmorecord.ViewRes)
	res.DxSmsMoRecordViewModel = data
	return
}