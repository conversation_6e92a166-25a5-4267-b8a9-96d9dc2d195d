// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxbusinessreporting"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxBusinessReporting = cDxBusinessReporting{}
)

type cDxBusinessReporting struct{}

// List 查看商机报备列表
func (c *cDxBusinessReporting) List(ctx context.Context, req *dxbusinessreporting.ListReq) (res *dxbusinessreporting.ListRes, err error) {
	list, totalCount, err := service.SysDxBusinessReporting().List(ctx, &req.DxBusinessReportingListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxBusinessReportingListModel{}
	}

	res = new(dxbusinessreporting.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出商机报备列表
func (c *cDxBusinessReporting) Export(ctx context.Context, req *dxbusinessreporting.ExportReq) (res *dxbusinessreporting.ExportRes, err error) {
	err = service.SysDxBusinessReporting().Export(ctx, &req.DxBusinessReportingListInp)
	return
}

// Edit 更新商机报备
func (c *cDxBusinessReporting) Edit(ctx context.Context, req *dxbusinessreporting.EditReq) (res *dxbusinessreporting.EditRes, err error) {
	err = service.SysDxBusinessReporting().Edit(ctx, &req.DxBusinessReportingEditInp)
	return
}

// View 获取指定商机报备信息
func (c *cDxBusinessReporting) View(ctx context.Context, req *dxbusinessreporting.ViewReq) (res *dxbusinessreporting.ViewRes, err error) {
	data, err := service.SysDxBusinessReporting().View(ctx, &req.DxBusinessReportingViewInp)
	if err != nil {
		return
	}

	res = new(dxbusinessreporting.ViewRes)
	res.DxBusinessReportingViewModel = data
	return
}

// Delete 删除商机报备
func (c *cDxBusinessReporting) Delete(ctx context.Context, req *dxbusinessreporting.DeleteReq) (res *dxbusinessreporting.DeleteRes, err error) {
	err = service.SysDxBusinessReporting().Delete(ctx, &req.DxBusinessReportingDeleteInp)
	return
}

// Status 更新商机报备状态
func (c *cDxBusinessReporting) Status(ctx context.Context, req *dxbusinessreporting.StatusReq) (res *dxbusinessreporting.StatusRes, err error) {
	err = service.SysDxBusinessReporting().Status(ctx, &req.DxBusinessReportingStatusInp)
	return
}

// StatTotalAmount 获取总金额统计
func (c *cDxBusinessReporting) StatTotalAmount(ctx context.Context, req *dxbusinessreporting.StatTotalAmountReq) (res *dxbusinessreporting.StatTotalAmountRes, err error) {
	data, err := service.SysDxBusinessReporting().StatTotalAmount(ctx, &sysin.DxBusinessReportingStatTotalAmountInp{
		DxBusinessReportingListInp: req.DxBusinessReportingListInp,
	})
	if err != nil {
		return nil, err
	}

	res = &dxbusinessreporting.StatTotalAmountRes{
		TotalBudgetAmount:      data.TotalBudgetAmount,
		TotalInvoiceAmount:     data.TotalInvoiceAmount,
		TotalPaymentAmount:     data.TotalPaymentAmount,
		TotalSettlementAmount:  data.TotalSettlementAmount,
		TotalDeliveryAmount:    data.TotalDeliveryAmount,
		TotalExpenditureAmount: data.TotalExpenditureAmount,
		TotalRetainedAmount:    data.TotalRetainedAmount,
		TotalRetentionRatio:    data.TotalRetentionRatio,
	}
	return
}

// ExportReconciliation 导出对账单
func (c *cDxBusinessReporting) ExportReconciliation(ctx context.Context, req *dxbusinessreporting.ExportReconciliationReq) (res *dxbusinessreporting.ExportReconciliationRes, err error) {
	err = service.SysDxBusinessReporting().ExportReconciliation(ctx, &req.DxBusinessReportingListInp)
	return
}

// Delivery 商机报备交付操作
func (c *cDxBusinessReporting) Delivery(ctx context.Context, req *dxbusinessreporting.DeliveryReq) (res *dxbusinessreporting.DeliveryRes, err error) {
	err = service.SysDxBusinessReporting().Delivery(ctx, &req.DxBusinessReportingDeliveryInp)
	return
}

// AssociateMember 关联客户
func (c *cDxBusinessReporting) AssociateMember(ctx context.Context, req *dxbusinessreporting.AssociateMemberReq) (res *dxbusinessreporting.AssociateMemberRes, err error) {
	err = service.SysDxBusinessReporting().AssociateMember(ctx, &req.DxBusinessReportingAssociateMemberInp)
	return
}

// StatusRollback 商机报备状态回退
func (c *cDxBusinessReporting) StatusRollback(ctx context.Context, req *dxbusinessreporting.StatusRollbackReq) (res *dxbusinessreporting.StatusRollbackRes, err error) {
	err = service.SysDxBusinessReporting().StatusRollback(ctx, &req.DxBusinessReportingStatusRollbackInp)
	return
}
