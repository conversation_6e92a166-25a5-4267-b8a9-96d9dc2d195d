// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmsgroupmobile"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsGroupMobile = cDxSmsGroupMobile{}
)

type cDxSmsGroupMobile struct{}

// List 查看群发号码列表
func (c *cDxSmsGroupMobile) List(ctx context.Context, req *dxsmsgroupmobile.ListReq) (res *dxsmsgroupmobile.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsGroupMobile().List(ctx, &req.DxSmsGroupMobileListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsGroupMobileListModel{}
	}

	res = new(dxsmsgroupmobile.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出群发号码列表
func (c *cDxSmsGroupMobile) Export(ctx context.Context, req *dxsmsgroupmobile.ExportReq) (res *dxsmsgroupmobile.ExportRes, err error) {
	err = service.SysDxSmsGroupMobile().Export(ctx, &req.DxSmsGroupMobileListInp)
	return
}

// Edit 更新群发号码
func (c *cDxSmsGroupMobile) Edit(ctx context.Context, req *dxsmsgroupmobile.EditReq) (res *dxsmsgroupmobile.EditRes, err error) {
	err = service.SysDxSmsGroupMobile().Edit(ctx, &req.DxSmsGroupMobileEditInp)
	return
}

// View 获取指定群发号码信息
func (c *cDxSmsGroupMobile) View(ctx context.Context, req *dxsmsgroupmobile.ViewReq) (res *dxsmsgroupmobile.ViewRes, err error) {
	data, err := service.SysDxSmsGroupMobile().View(ctx, &req.DxSmsGroupMobileViewInp)
	if err != nil {
		return
	}

	res = new(dxsmsgroupmobile.ViewRes)
	res.DxSmsGroupMobileViewModel = data
	return
}

// Delete 删除群发号码
func (c *cDxSmsGroupMobile) Delete(ctx context.Context, req *dxsmsgroupmobile.DeleteReq) (res *dxsmsgroupmobile.DeleteRes, err error) {
	err = service.SysDxSmsGroupMobile().Delete(ctx, &req.DxSmsGroupMobileDeleteInp)
	return
}