// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmschargerecord"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsChargeRecord = cDxSmsChargeRecord{}
)

type cDxSmsChargeRecord struct{}

// List 查看充值记录列表
func (c *cDxSmsChargeRecord) List(ctx context.Context, req *dxsmschargerecord.ListReq) (res *dxsmschargerecord.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsChargeRecord().List(ctx, &req.DxSmsChargeRecordListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsChargeRecordListModel{}
	}

	res = new(dxsmschargerecord.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出充值记录列表
func (c *cDxSmsChargeRecord) Export(ctx context.Context, req *dxsmschargerecord.ExportReq) (res *dxsmschargerecord.ExportRes, err error) {
	err = service.SysDxSmsChargeRecord().Export(ctx, &req.DxSmsChargeRecordListInp)
	return
}

// Edit 更新充值记录
func (c *cDxSmsChargeRecord) Edit(ctx context.Context, req *dxsmschargerecord.EditReq) (res *dxsmschargerecord.EditRes, err error) {
	err = service.SysDxSmsChargeRecord().Edit(ctx, &req.DxSmsChargeRecordEditInp)
	return
}

// View 获取指定充值记录信息
func (c *cDxSmsChargeRecord) View(ctx context.Context, req *dxsmschargerecord.ViewReq) (res *dxsmschargerecord.ViewRes, err error) {
	data, err := service.SysDxSmsChargeRecord().View(ctx, &req.DxSmsChargeRecordViewInp)
	if err != nil {
		return
	}

	res = new(dxsmschargerecord.ViewRes)
	res.DxSmsChargeRecordViewModel = data
	return
}

// Delete 删除充值记录
func (c *cDxSmsChargeRecord) Delete(ctx context.Context, req *dxsmschargerecord.DeleteReq) (res *dxsmschargerecord.DeleteRes, err error) {
	err = service.SysDxSmsChargeRecord().Delete(ctx, &req.DxSmsChargeRecordDeleteInp)
	return
}