// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmssign"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsSign = cDxSmsSign{}
)

type cDxSmsSign struct{}

// List 查看短信签名列表
func (c *cDxSmsSign) List(ctx context.Context, req *dxsmssign.ListReq) (res *dxsmssign.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsSign().List(ctx, &req.DxSmsSignListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsSignListModel{}
	}

	res = new(dxsmssign.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出短信签名列表
func (c *cDxSmsSign) Export(ctx context.Context, req *dxsmssign.ExportReq) (res *dxsmssign.ExportRes, err error) {
	err = service.SysDxSmsSign().Export(ctx, &req.DxSmsSignListInp)
	return
}

// Edit 更新短信签名
func (c *cDxSmsSign) Edit(ctx context.Context, req *dxsmssign.EditReq) (res *dxsmssign.EditRes, err error) {
	err = service.SysDxSmsSign().Edit(ctx, &req.DxSmsSignEditInp)
	return
}

// View 获取指定短信签名信息
func (c *cDxSmsSign) View(ctx context.Context, req *dxsmssign.ViewReq) (res *dxsmssign.ViewRes, err error) {
	data, err := service.SysDxSmsSign().View(ctx, &req.DxSmsSignViewInp)
	if err != nil {
		return
	}

	res = new(dxsmssign.ViewRes)
	res.DxSmsSignViewModel = data
	return
}

// Delete 删除短信签名
func (c *cDxSmsSign) Delete(ctx context.Context, req *dxsmssign.DeleteReq) (res *dxsmssign.DeleteRes, err error) {
	err = service.SysDxSmsSign().Delete(ctx, &req.DxSmsSignDeleteInp)
	return
}

// Status 更新短信签名状态
func (c *cDxSmsSign) Status(ctx context.Context, req *dxsmssign.StatusReq) (res *dxsmssign.StatusRes, err error) {
	err = service.SysDxSmsSign().Status(ctx, &req.DxSmsSignStatusInp)
	return
}

// Option 查看短信签名选项列表
func (c *cDxSmsSign) Option(ctx context.Context, req *dxsmssign.OptionReq) (res *dxsmssign.OptionRes, err error) {
	list, err := service.SysDxSmsSign().Option(ctx, &req.DxSmsSignOptionInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsSignOption{}
	}

	res = new(dxsmssign.OptionRes)
	res.List = list
	return
}
