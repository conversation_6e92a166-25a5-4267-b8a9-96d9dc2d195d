// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/companyaccount"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	CompanyAccount = cCompanyAccount{}
)

type cCompanyAccount struct{}

// List 查看企业账户列表
func (c *cCompanyAccount) List(ctx context.Context, req *companyaccount.ListReq) (res *companyaccount.ListRes, err error) {
	list, totalCount, err := service.SysCompanyAccount().List(ctx, &req.CompanyAccountListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.CompanyAccountListModel{}
	}

	res = new(companyaccount.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出企业账户列表
func (c *cCompanyAccount) Export(ctx context.Context, req *companyaccount.ExportReq) (res *companyaccount.ExportRes, err error) {
	err = service.SysCompanyAccount().Export(ctx, &req.CompanyAccountListInp)
	return
}

// Edit 更新企业账户
func (c *cCompanyAccount) Edit(ctx context.Context, req *companyaccount.EditReq) (res *companyaccount.EditRes, err error) {
	err = service.SysCompanyAccount().Edit(ctx, &req.CompanyAccountEditInp)
	return
}

// MaxSort 获取企业账户最大排序
func (c *cCompanyAccount) MaxSort(ctx context.Context, req *companyaccount.MaxSortReq) (res *companyaccount.MaxSortRes, err error) {
	data, err := service.SysCompanyAccount().MaxSort(ctx, &req.CompanyAccountMaxSortInp)
	if err != nil {
		return
	}

	res = new(companyaccount.MaxSortRes)
	res.CompanyAccountMaxSortModel = data
	return
}

// View 获取指定企业账户信息
func (c *cCompanyAccount) View(ctx context.Context, req *companyaccount.ViewReq) (res *companyaccount.ViewRes, err error) {
	data, err := service.SysCompanyAccount().View(ctx, &req.CompanyAccountViewInp)
	if err != nil {
		return
	}

	res = new(companyaccount.ViewRes)
	res.CompanyAccountViewModel = data
	return
}

// Delete 删除企业账户
func (c *cCompanyAccount) Delete(ctx context.Context, req *companyaccount.DeleteReq) (res *companyaccount.DeleteRes, err error) {
	err = service.SysCompanyAccount().Delete(ctx, &req.CompanyAccountDeleteInp)
	return
}