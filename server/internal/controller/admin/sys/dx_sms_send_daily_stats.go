// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmssenddailystats"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsSendDailyStats = cDxSmsSendDailyStats{}
)

type cDxSmsSendDailyStats struct{}

// List 查看短信报表列表
func (c *cDxSmsSendDailyStats) List(ctx context.Context, req *dxsmssenddailystats.ListReq) (res *dxsmssenddailystats.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsSendDailyStats().List(ctx, &req.DxSmsSendDailyStatsListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsSendDailyStatsListModel{}
	}

	res = new(dxsmssenddailystats.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出短信报表列表
func (c *cDxSmsSendDailyStats) Export(ctx context.Context, req *dxsmssenddailystats.ExportReq) (res *dxsmssenddailystats.ExportRes, err error) {
	err = service.SysDxSmsSendDailyStats().Export(ctx, &req.DxSmsSendDailyStatsListInp)
	return
}

// Summary 获取短信报表统计汇总
func (c *cDxSmsSendDailyStats) Summary(ctx context.Context, req *dxsmssenddailystats.SummaryReq) (res *dxsmssenddailystats.SummaryRes, err error) {
	summary, err := service.SysDxSmsSendDailyStats().Summary(ctx, &req.DxSmsSendDailyStatsSummaryInp)
	if err != nil {
		return
	}

	res = &dxsmssenddailystats.SummaryRes{
		DxSmsSendDailyStatsSummaryModel: *summary,
	}
	return
}
