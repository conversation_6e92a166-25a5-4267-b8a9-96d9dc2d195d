// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmssendrecord"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsSendRecord = cDxSmsSendRecord{}
)

type cDxSmsSendRecord struct{}

// List 查看短信记录列表
func (c *cDxSmsSendRecord) List(ctx context.Context, req *dxsmssendrecord.ListReq) (res *dxsmssendrecord.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsSendRecord().List(ctx, &req.DxSmsSendRecordListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsSendRecordListModel{}
	}

	res = new(dxsmssendrecord.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出短信记录列表
func (c *cDxSmsSendRecord) Export(ctx context.Context, req *dxsmssendrecord.ExportReq) (res *dxsmssendrecord.ExportRes, err error) {
	err = service.SysDxSmsSendRecord().Export(ctx, &req.DxSmsSendRecordListInp)
	return
}

// View 获取指定短信记录信息
func (c *cDxSmsSendRecord) View(ctx context.Context, req *dxsmssendrecord.ViewReq) (res *dxsmssendrecord.ViewRes, err error) {
	data, err := service.SysDxSmsSendRecord().View(ctx, &req.DxSmsSendRecordViewInp)
	if err != nil {
		return
	}

	res = new(dxsmssendrecord.ViewRes)
	res.DxSmsSendRecordViewModel = data
	return
}

// ResendFailed 重发失败的短信记录
func (c *cDxSmsSendRecord) ResendFailed(ctx context.Context, req *dxsmssendrecord.ResendFailedReq) (res *dxsmssendrecord.ResendFailedRes, err error) {
	model, err := service.SysDxSmsSendRecord().ResendFailed(ctx, &req.DxSmsSendRecordResendFailedInp)
	if err != nil {
		return nil, err
	}

	res = new(dxsmssendrecord.ResendFailedRes)
	res.DxSmsSendRecordResendFailedModel = model
	return
}

// StatTotalBilling 获取短信计费条数统计
func (c *cDxSmsSendRecord) StatTotalBilling(ctx context.Context, req *dxsmssendrecord.StatTotalBillingReq) (res *dxsmssendrecord.StatTotalBillingRes, err error) {
	data, err := service.SysDxSmsSendRecord().StatTotalBilling(ctx, &req.DxSmsSendRecordListInp)
	if err != nil {
		return nil, err
	}

	res = new(dxsmssendrecord.StatTotalBillingRes)
	res.TotalBillingCount = data.TotalBillingCount
	return
}

// FailureLogList 获取失败重发明细列表
func (c *cDxSmsSendRecord) FailureLogList(ctx context.Context, req *dxsmssendrecord.FailureLogListReq) (res *dxsmssendrecord.FailureLogListRes, err error) {
	list, totalCount, err := service.SysDxSmsSendRecord().FailureLogList(ctx, &req.DxSmsFailureLogListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsFailureLogListModel{}
	}

	res = new(dxsmssendrecord.FailureLogListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}
