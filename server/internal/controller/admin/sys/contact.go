// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/contact"
	"hotgo/internal/model/input/adminin"
	"hotgo/internal/model/input/form"
	"hotgo/internal/service"
)

var Contact = cContact{}

type cContact struct{}

// List 查看通讯录联系人列表
func (c *cContact) List(ctx context.Context, req *contact.ListReq) (res *contact.ListRes, err error) {
	list, totalCount, err := service.SysContact().List(ctx, &req.ContactListInp)
	if err != nil {
		return
	}

	// 转换指针数组为值数组
	contactList := make([]adminin.ContactListModel, len(list))
	for i, item := range list {
		if item != nil {
			contactList[i] = *item
		}
	}

	res = &contact.ListRes{
		List:           contactList,
		Total:          totalCount,
		TotalCount:     totalCount,
		PageCount:      form.CalPageCount(totalCount, req.ContactListInp.PerPage),
		ContactListInp: &req.ContactListInp,
	}
	return
}

// Export 导出通讯录联系人
func (c *cContact) Export(ctx context.Context, req *contact.ExportReq) (res *contact.ExportRes, err error) {
	err = service.SysContact().Export(ctx, &req.ContactListInp)
	return
}

// Edit 修改/新增通讯录联系人
func (c *cContact) Edit(ctx context.Context, req *contact.EditReq) (res *contact.EditRes, err error) {
	err = service.SysContact().Edit(ctx, &req.ContactEditInp)
	return
}

// View 获取指定通讯录联系人信息
func (c *cContact) View(ctx context.Context, req *contact.ViewReq) (res *contact.ViewRes, err error) {
	data, err := service.SysContact().View(ctx, &req.ContactViewInp)
	if err != nil {
		return
	}

	res = &contact.ViewRes{ContactViewModel: data}
	return
}

// Delete 删除通讯录联系人
func (c *cContact) Delete(ctx context.Context, req *contact.DeleteReq) (res *contact.DeleteRes, err error) {
	err = service.SysContact().Delete(ctx, &req.ContactDeleteInp)
	return
}

// Status 更新通讯录联系人状态
func (c *cContact) Status(ctx context.Context, req *contact.StatusReq) (res *contact.StatusRes, err error) {
	err = service.SysContact().Status(ctx, &req.ContactStatusInp)
	return
}

// GroupStats 获取分组统计
func (c *cContact) GroupStats(ctx context.Context, req *contact.GroupStatsReq) (res *contact.GroupStatsRes, err error) {
	groups, total, ungrouped, err := service.SysContact().GroupStats(ctx)
	if err != nil {
		return
	}

	res = &contact.GroupStatsRes{
		Groups:    groups,
		Total:     total,
		Ungrouped: ungrouped,
	}
	return
}

// ListByGroup 按分组筛选联系人
func (c *cContact) ListByGroup(ctx context.Context, req *contact.ListByGroupReq) (res *contact.ListByGroupRes, err error) {
	list, totalCount, err := service.SysContact().ListByGroup(ctx, &req.ContactListByGroupInp)
	if err != nil {
		return
	}

	// 转换指针数组为值数组
	contactWithGroupsList := make([]adminin.ContactWithGroupsModel, len(list))
	for i, item := range list {
		if item != nil {
			contactWithGroupsList[i] = *item
		}
	}

	res = &contact.ListByGroupRes{
		List:                  contactWithGroupsList,
		Total:                 totalCount,
		TotalCount:            totalCount,
		PageCount:             form.CalPageCount(totalCount, req.ContactListByGroupInp.PerPage),
		ContactListByGroupInp: &req.ContactListByGroupInp,
	}
	return
}

// UpdateGroups 更新联系人分组
func (c *cContact) UpdateGroups(ctx context.Context, req *contact.UpdateGroupsReq) (res *contact.UpdateGroupsRes, err error) {
	err = service.SysContact().UpdateGroups(ctx, &req.ContactUpdateGroupsInp)
	return
}

// Import 批量导入通讯录联系人
func (c *cContact) Import(ctx context.Context, req *contact.ImportReq) (res *contact.ImportRes, err error) {
	err = service.SysContact().Import(ctx, &req.ContactImportInp)
	return
}

// CheckImportFile 检查导入文件
func (c *cContact) CheckImportFile(ctx context.Context, req *contact.CheckImportFileReq) (res *contact.CheckImportFileRes, err error) {
	data, err := service.SysContact().CheckImportFile(ctx, &req.ContactCheckImportFileInp)
	if err != nil {
		return
	}

	res = &contact.CheckImportFileRes{ContactCheckImportFileModel: data}
	return
}
