// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/dxsmsauditlog"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	DxSmsAuditLog = cDxSmsAuditLog{}
)

type cDxSmsAuditLog struct{}

// List 查看审核日志列表
func (c *cDxSmsAuditLog) List(ctx context.Context, req *dxsmsauditlog.ListReq) (res *dxsmsauditlog.ListRes, err error) {
	list, totalCount, err := service.SysDxSmsAuditLog().List(ctx, &req.DxSmsAuditLogListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsAuditLogListModel{}
	}

	res = new(dxsmsauditlog.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出审核日志列表
func (c *cDxSmsAuditLog) Export(ctx context.Context, req *dxsmsauditlog.ExportReq) (res *dxsmsauditlog.ExportRes, err error) {
	err = service.SysDxSmsAuditLog().Export(ctx, &req.DxSmsAuditLogListInp)
	return
}

// Edit 更新/新增审核日志
func (c *cDxSmsAuditLog) Edit(ctx context.Context, req *dxsmsauditlog.EditReq) (res *dxsmsauditlog.EditRes, err error) {
	err = service.SysDxSmsAuditLog().Edit(ctx, &req.DxSmsAuditLogEditInp)
	return
}

// View 获取指定审核日志信息
func (c *cDxSmsAuditLog) View(ctx context.Context, req *dxsmsauditlog.ViewReq) (res *dxsmsauditlog.ViewRes, err error) {
	data, err := service.SysDxSmsAuditLog().View(ctx, &req.DxSmsAuditLogViewInp)
	if err != nil {
		return
	}

	res = new(dxsmsauditlog.ViewRes)
	res.DxSmsAuditLogViewModel = data
	return
}

// ViewByPkId 获取指定审核日志信息
func (c *cDxSmsAuditLog) ViewByPkId(ctx context.Context, req *dxsmsauditlog.ViewByPkIdReq) (res *dxsmsauditlog.ViewByPkIdRes, err error) {
	data, err := service.SysDxSmsAuditLog().ViewByPkId(ctx, &req.DxSmsAuditLogViewByPkIdInp)
	if err != nil {
		return
	}

	res = new(dxsmsauditlog.ViewByPkIdRes)
	res.DxSmsAuditLogViewModel = data
	return
}

// ListByPkId 查看审核日志列表
func (c *cDxSmsAuditLog) ListByPkId(ctx context.Context, req *dxsmsauditlog.ListByPkIdReq) (res *dxsmsauditlog.ListByPkIdRes, err error) {
	list, totalCount, err := service.SysDxSmsAuditLog().ListByPkId(ctx, &req.DxSmsAuditLogListByPkIdInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.DxSmsAuditLogListModel{}
	}

	res = new(dxsmsauditlog.ListByPkIdRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}
