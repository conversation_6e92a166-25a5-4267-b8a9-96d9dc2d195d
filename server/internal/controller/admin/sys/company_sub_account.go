// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"context"
	"hotgo/api/admin/companysubaccount"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
)

var (
	CompanySubAccount = cCompanySubAccount{}
)

type cCompanySubAccount struct{}

// List 查看企业子账户列表
func (c *cCompanySubAccount) List(ctx context.Context, req *companysubaccount.ListReq) (res *companysubaccount.ListRes, err error) {
	list, totalCount, err := service.SysCompanySubAccount().List(ctx, &req.CompanySubAccountListInp)
	if err != nil {
		return
	}

	if list == nil {
		list = []*sysin.CompanySubAccountListModel{}
	}

	res = new(companysubaccount.ListRes)
	res.List = list
	res.PageRes.Pack(req, totalCount)
	return
}

// Export 导出企业子账户列表
func (c *cCompanySubAccount) Export(ctx context.Context, req *companysubaccount.ExportReq) (res *companysubaccount.ExportRes, err error) {
	err = service.SysCompanySubAccount().Export(ctx, &req.CompanySubAccountListInp)
	return
}

// Edit 更新企业子账户
func (c *cCompanySubAccount) Edit(ctx context.Context, req *companysubaccount.EditReq) (res *companysubaccount.EditRes, err error) {
	err = service.SysCompanySubAccount().Edit(ctx, &req.CompanySubAccountEditInp)
	return
}

// MaxSort 获取企业子账户最大排序
func (c *cCompanySubAccount) MaxSort(ctx context.Context, req *companysubaccount.MaxSortReq) (res *companysubaccount.MaxSortRes, err error) {
	data, err := service.SysCompanySubAccount().MaxSort(ctx, &req.CompanySubAccountMaxSortInp)
	if err != nil {
		return
	}

	res = new(companysubaccount.MaxSortRes)
	res.CompanySubAccountMaxSortModel = data
	return
}

// View 获取指定企业子账户信息
func (c *cCompanySubAccount) View(ctx context.Context, req *companysubaccount.ViewReq) (res *companysubaccount.ViewRes, err error) {
	data, err := service.SysCompanySubAccount().View(ctx, &req.CompanySubAccountViewInp)
	if err != nil {
		return
	}

	res = new(companysubaccount.ViewRes)
	res.CompanySubAccountViewModel = data
	return
}

// Delete 删除企业子账户
func (c *cCompanySubAccount) Delete(ctx context.Context, req *companysubaccount.DeleteReq) (res *companysubaccount.DeleteRes, err error) {
	err = service.SysCompanySubAccount().Delete(ctx, &req.CompanySubAccountDeleteInp)
	return
}

// Status 更新企业子账户状态
func (c *cCompanySubAccount) Status(ctx context.Context, req *companysubaccount.StatusReq) (res *companysubaccount.StatusRes, err error) {
	err = service.SysCompanySubAccount().Status(ctx, &req.CompanySubAccountStatusInp)
	return
}

// AddBalance 变更企业子账户余额
func (c *cCompanySubAccount) AddBalance(ctx context.Context, req *companysubaccount.AddBalanceReq) (res *companysubaccount.AddBalanceRes, err error) {
	err = service.SysCompanySubAccount().AddBalance(ctx, &req.CompanySubAccountAddBalanceInp)
	return
}

// AddSmsBalance 企业子账户短信充值
func (c *cCompanySubAccount) AddSmsBalance(ctx context.Context, req *companysubaccount.AddSmsBalanceReq) (res *companysubaccount.AddSmsBalanceRes, err error) {
	err = service.SysCompanySubAccount().AddSmsBalance(ctx, &req.CompanySubAccountAddSmsBalanceInp)
	return
}
