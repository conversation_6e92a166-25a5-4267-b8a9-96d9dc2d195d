// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"hotgo/internal/library/hgorm/handler"
	"hotgo/internal/model"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/adminin"
	"hotgo/internal/model/input/sysin"
	"hotgo/utility/tree"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	ISysAddons interface {
		// List 获取列表
		List(ctx context.Context, in *sysin.AddonsListInp) (list []*sysin.AddonsListModel, totalCount int, err error)
		// Build 提交生成
		Build(ctx context.Context, in *sysin.AddonsBuildInp) (err error)
		// Install 安装模块
		Install(ctx context.Context, in *sysin.AddonsInstallInp) (err error)
		// Upgrade 更新模块
		Upgrade(ctx context.Context, in *sysin.AddonsUpgradeInp) (err error)
		// UnInstall 卸载模块
		UnInstall(ctx context.Context, in *sysin.AddonsUnInstallInp) (err error)
	}
	ISysAddonsConfig interface {
		// GetConfigByGroup 获取指定分组的配置
		GetConfigByGroup(ctx context.Context, in *sysin.GetAddonsConfigInp) (res *sysin.GetAddonsConfigModel, err error)
		// ConversionType 转换类型
		ConversionType(ctx context.Context, models *entity.SysAddonsConfig) (value interface{}, err error)
		// UpdateConfigByGroup 更新指定分组的配置
		UpdateConfigByGroup(ctx context.Context, in *sysin.UpdateAddonsConfigInp) (err error)
	}
	ISysAdminCompany interface {
		// Model 企业列表ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取企业列表列表
		List(ctx context.Context, in *sysin.AdminCompanyListInp) (list []*sysin.AdminCompanyListModel, totalCount int, err error)
		// Export 导出企业列表
		Export(ctx context.Context, in *sysin.AdminCompanyListInp) (err error)
		// Edit 修改/新增企业列表
		Edit(ctx context.Context, in *sysin.AdminCompanyEditInp) (err error)
		// Delete 删除企业列表
		Delete(ctx context.Context, in *sysin.AdminCompanyDeleteInp) (err error)
		// MaxSort 获取企业列表最大排序
		MaxSort(ctx context.Context, in *sysin.AdminCompanyMaxSortInp) (res *sysin.AdminCompanyMaxSortModel, err error)
		// View 获取企业列表指定信息
		View(ctx context.Context, in *sysin.AdminCompanyViewInp) (res *sysin.AdminCompanyViewModel, err error)
		// TreeOption 获取企业列表关系树选项
		TreeOption(ctx context.Context) (nodes []tree.Node, err error)
		// Status 更新企业状态
		Status(ctx context.Context, in *sysin.AdminCompanyStatusInp) (err error)
		// AddBalance 变更余额
		AddBalance(ctx context.Context, in *sysin.AdminCompanyAddBalanceInp) (err error)
		// AddSmsBalance 短信充值
		AddSmsBalance(ctx context.Context, in *sysin.AdminCompanyAddSMSBalanceInp) (err error)
		// Workbench 工作台
		Workbench(ctx context.Context, in *sysin.AdminCompanyWorkbenchInp) (res *sysin.AdminCompanyWorkbenchModel, err error)
		// CompanyTree 获取企业树形结构
		CompanyTree(ctx context.Context, in *sysin.CompanyTreeInp) (res []tree.Node, err error)
	}
	ISysAttachment interface {
		// Model ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// Delete 删除附件
		Delete(ctx context.Context, in *sysin.AttachmentDeleteInp) (err error)
		// View 获取附件信息
		View(ctx context.Context, in *sysin.AttachmentViewInp) (res *sysin.AttachmentViewModel, err error)
		// List 获取附件列表
		List(ctx context.Context, in *sysin.AttachmentListInp) (list []*sysin.AttachmentListModel, totalCount int, err error)
		// ClearKind 清空上传类型
		ClearKind(ctx context.Context, in *sysin.AttachmentClearKindInp) (err error)
		// AttachmentKindOption 上传类型选项
		AttachmentKindOption(ctx context.Context) (opts []*model.Option, err error)
	}
	ISysBlacklist interface {
		// Delete 删除
		Delete(ctx context.Context, in *sysin.BlacklistDeleteInp) (err error)
		// Edit 修改/新增
		Edit(ctx context.Context, in *sysin.BlacklistEditInp) (err error)
		// Status 更新状态
		Status(ctx context.Context, in *sysin.BlacklistStatusInp) (err error)
		// View 获取指定信息
		View(ctx context.Context, in *sysin.BlacklistViewInp) (res *sysin.BlacklistViewModel, err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.BlacklistListInp) (list []*sysin.BlacklistListModel, totalCount int, err error)
		// VariableLoad 变化加载
		VariableLoad(ctx context.Context, err error)
		// Load 加载黑名单
		Load(ctx context.Context)
		// VerifyRequest 验证请求的访问IP是否在黑名单，如果存在则返回错误
		VerifyRequest(r *ghttp.Request) (err error)
		// ClusterSync 集群同步
		ClusterSync(ctx context.Context, message *gredis.Message)
	}
	ISysCompanyAccount interface {
		// Model 企业账户ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取企业账户列表
		List(ctx context.Context, in *sysin.CompanyAccountListInp) (list []*sysin.CompanyAccountListModel, totalCount int, err error)
		// Export 导出企业账户
		Export(ctx context.Context, in *sysin.CompanyAccountListInp) (err error)
		// Edit 修改/新增企业账户
		Edit(ctx context.Context, in *sysin.CompanyAccountEditInp) (err error)
		// Delete 删除企业账户
		Delete(ctx context.Context, in *sysin.CompanyAccountDeleteInp) (err error)
		// MaxSort 获取企业账户最大排序
		MaxSort(ctx context.Context, in *sysin.CompanyAccountMaxSortInp) (res *sysin.CompanyAccountMaxSortModel, err error)
		// View 获取企业账户指定信息
		View(ctx context.Context, in *sysin.CompanyAccountViewInp) (res *sysin.CompanyAccountViewModel, err error)
	}
	ISysCompanySubAccount interface {
		// Model 企业子账户ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取企业子账户列表
		List(ctx context.Context, in *sysin.CompanySubAccountListInp) (list []*sysin.CompanySubAccountListModel, totalCount int, err error)
		// Export 导出企业子账户
		Export(ctx context.Context, in *sysin.CompanySubAccountListInp) (err error)
		// Edit 修改/新增企业子账户
		Edit(ctx context.Context, in *sysin.CompanySubAccountEditInp) (err error)
		// Delete 删除企业子账户
		Delete(ctx context.Context, in *sysin.CompanySubAccountDeleteInp) (err error)
		// MaxSort 获取企业子账户最大排序
		MaxSort(ctx context.Context, in *sysin.CompanySubAccountMaxSortInp) (res *sysin.CompanySubAccountMaxSortModel, err error)
		// View 获取企业子账户指定信息
		View(ctx context.Context, in *sysin.CompanySubAccountViewInp) (res *sysin.CompanySubAccountViewModel, err error)
		// Status 更新企业子账户状态
		Status(ctx context.Context, in *sysin.CompanySubAccountStatusInp) (err error)
		// AddBalance 变更企业子账户余额
		AddBalance(ctx context.Context, in *sysin.CompanySubAccountAddBalanceInp) (err error)
		// AddSmsBalance 企业子账户短信充值
		AddSmsBalance(ctx context.Context, in *sysin.CompanySubAccountAddSmsBalanceInp) (err error)
	}
	ISysConfig interface {
		// InitConfig 初始化系统配置
		InitConfig(ctx context.Context)
		// LoadConfig 加载系统配置
		LoadConfig(ctx context.Context) (err error)
		// GetLogin 获取登录配置
		GetLogin(ctx context.Context) (conf *model.LoginConfig, err error)
		// GetWechat 获取微信配置
		GetWechat(ctx context.Context) (conf *model.WechatConfig, err error)
		// GetPay 获取支付配置
		GetPay(ctx context.Context) (conf *model.PayConfig, err error)
		// GetSms 获取短信配置
		GetSms(ctx context.Context) (conf *model.SmsConfig, err error)
		// GetGeo 获取地理配置
		GetGeo(ctx context.Context) (conf *model.GeoConfig, err error)
		// GetUpload 获取上传配置
		GetUpload(ctx context.Context) (conf *model.UploadConfig, err error)
		// GetSmtp 获取邮件配置
		GetSmtp(ctx context.Context) (conf *model.EmailConfig, err error)
		// GetBasic 获取基础配置
		GetBasic(ctx context.Context) (conf *model.BasicConfig, err error)
		// GetLoadTCP 获取本地tcp配置
		GetLoadTCP(ctx context.Context) (conf *model.TCPConfig, err error)
		// GetLoadGenerate 获取本地生成配置
		GetLoadGenerate(ctx context.Context) (conf *model.GenerateConfig, err error)
		// GetLoadToken 获取本地token配置
		GetLoadToken(ctx context.Context) (conf *model.TokenConfig, err error)
		// GetLoadLog 获取本地日志配置
		GetLoadLog(ctx context.Context) (conf *model.LogConfig, err error)
		// GetLoadServeLog 获取本地服务日志配置
		GetLoadServeLog(ctx context.Context) (conf *model.ServeLogConfig, err error)
		// GetConfigByGroup 获取指定分组的配置
		GetConfigByGroup(ctx context.Context, in *sysin.GetConfigInp) (res *sysin.GetConfigModel, err error)
		// ConversionType 转换类型
		ConversionType(ctx context.Context, models *entity.SysConfig) (value interface{}, err error)
		// UpdateConfigByGroup 更新指定分组的配置
		UpdateConfigByGroup(ctx context.Context, in *sysin.UpdateConfigInp) (err error)
		// ClusterSync 集群同步
		ClusterSync(ctx context.Context, message *gredis.Message)
	}
	ISysContact interface {
		// Model 通讯录联系人ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取通讯录联系人列表
		List(ctx context.Context, in *adminin.ContactListInp) (list []*adminin.ContactListModel, totalCount int, err error)
		// Export 导出通讯录联系人
		Export(ctx context.Context, in *adminin.ContactListInp) (err error)
		// Edit 修改/新增通讯录联系人
		Edit(ctx context.Context, in *adminin.ContactEditInp) (err error)
		// Delete 删除通讯录联系人
		Delete(ctx context.Context, in *adminin.ContactDeleteInp) (err error)
		// View 获取指定通讯录联系人信息
		View(ctx context.Context, in *adminin.ContactViewInp) (res *adminin.ContactViewModel, err error)
		// Status 更新通讯录联系人状态
		Status(ctx context.Context, in *adminin.ContactStatusInp) (err error)
		// GroupStats 获取分组统计
		GroupStats(ctx context.Context) (groups []map[string]interface{}, total int, ungrouped int, err error)
		// ListByGroup 按分组筛选联系人
		ListByGroup(ctx context.Context, in *adminin.ContactListByGroupInp) (list []*adminin.ContactWithGroupsModel, totalCount int, err error)
		// UpdateGroups 更新联系人分组
		UpdateGroups(ctx context.Context, in *adminin.ContactUpdateGroupsInp) (err error)
		// Import 批量导入联系人
		Import(ctx context.Context, in *adminin.ContactImportInp) (err error)
		// CheckImportFile 检查导入文件
		CheckImportFile(ctx context.Context, in *adminin.ContactCheckImportFileInp) (res *adminin.ContactCheckImportFileModel, err error)
	}
	ISysContactGroup interface {
		// Model 通讯录分组ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取通讯录分组列表
		List(ctx context.Context, in *adminin.ContactGroupListInp) (list interface{}, totalCount int, err error)
		// Edit 修改/新增通讯录分组
		Edit(ctx context.Context, in *adminin.ContactGroupEditInp) (err error)
		// Delete 删除通讯录分组
		Delete(ctx context.Context, in *adminin.ContactGroupDeleteInp) (err error)
		// View 获取指定通讯录分组信息
		View(ctx context.Context, in *adminin.ContactGroupViewInp) (res *adminin.ContactGroupViewModel, err error)
		// Status 更新通讯录分组状态
		Status(ctx context.Context, in *adminin.ContactGroupStatusInp) (err error)
		// TreeOption 获取关系树选项
		TreeOption(ctx context.Context) (list []tree.Node, err error)
	}
	ISysCron interface {
		StartCron(ctx context.Context)
		// Delete 删除
		Delete(ctx context.Context, in *sysin.CronDeleteInp) (err error)
		// Edit 修改/新增
		Edit(ctx context.Context, in *sysin.CronEditInp) (err error)
		// Status 更新状态
		Status(ctx context.Context, in *sysin.CronStatusInp) (err error)
		// MaxSort 最大排序
		MaxSort(ctx context.Context, in *sysin.CronMaxSortInp) (res *sysin.CronMaxSortModel, err error)
		// View 获取指定信息
		View(ctx context.Context, in *sysin.CronViewInp) (res *sysin.CronViewModel, err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.CronListInp) (list []*sysin.CronListModel, totalCount int, err error)
		// GetName 获取分组名称
		GetName(ctx context.Context, id int64) (name string, err error)
		// OnlineExec 在线执行
		OnlineExec(ctx context.Context, in *sysin.OnlineExecInp) (err error)
		// DispatchLog 查看指定任务的调度日志
		DispatchLog(ctx context.Context, in *sysin.DispatchLogInp) (res *sysin.DispatchLogModel, err error)
	}
	ISysCronGroup interface {
		// Delete 删除
		Delete(ctx context.Context, in *sysin.CronGroupDeleteInp) (err error)
		// Edit 修改/新增
		Edit(ctx context.Context, in *sysin.CronGroupEditInp) (err error)
		// Status 更新状态
		Status(ctx context.Context, in *sysin.CronGroupStatusInp) (err error)
		// MaxSort 最大排序
		MaxSort(ctx context.Context, in *sysin.CronGroupMaxSortInp) (res *sysin.CronGroupMaxSortModel, err error)
		// View 获取指定信息
		View(ctx context.Context, in *sysin.CronGroupViewInp) (res *sysin.CronGroupViewModel, err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.CronGroupListInp) (list []*sysin.CronGroupListModel, totalCount int, err error)
		// Select 选项
		Select(ctx context.Context, in *sysin.CronGroupSelectInp) (res *sysin.CronGroupSelectModel, err error)
	}
	ISysCurdDemo interface {
		// Model CURD列表ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取CURD列表列表
		List(ctx context.Context, in *sysin.CurdDemoListInp) (list []*sysin.CurdDemoListModel, totalCount int, err error)
		// Export 导出CURD列表
		Export(ctx context.Context, in *sysin.CurdDemoListInp) (err error)
		// Edit 修改/新增CURD列表
		Edit(ctx context.Context, in *sysin.CurdDemoEditInp) (err error)
		// Delete 删除CURD列表
		Delete(ctx context.Context, in *sysin.CurdDemoDeleteInp) (err error)
		// MaxSort 获取CURD列表最大排序
		MaxSort(ctx context.Context, in *sysin.CurdDemoMaxSortInp) (res *sysin.CurdDemoMaxSortModel, err error)
		// View 获取CURD列表指定信息
		View(ctx context.Context, in *sysin.CurdDemoViewInp) (res *sysin.CurdDemoViewModel, err error)
		// Status 更新CURD列表状态
		Status(ctx context.Context, in *sysin.CurdDemoStatusInp) (err error)
		// Switch 更新CURD列表开关
		Switch(ctx context.Context, in *sysin.CurdDemoSwitchInp) (err error)
	}
	ISysDictData interface {
		// Delete 删除
		Delete(ctx context.Context, in *sysin.DictDataDeleteInp) error
		// Edit 修改/新增
		Edit(ctx context.Context, in *sysin.DictDataEditInp) (err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.DictDataListInp) (list []*sysin.DictDataListModel, totalCount int, err error)
		// GetId 获取指定类型的ID
		GetId(ctx context.Context, t string) (id int64, err error)
		// GetType 获取指定ID的类型标识
		GetType(ctx context.Context, id int64) (types string, err error)
		// GetTypes 获取指定ID的所有类型标识，包含下级
		GetTypes(ctx context.Context, id int64) (types []string, err error)
		// Select 获取列表
		Select(ctx context.Context, in *sysin.DataSelectInp) (list sysin.DataSelectModel, err error)
	}
	ISysDictType interface {
		// Tree 树
		Tree(ctx context.Context) (list []*sysin.DictTypeTree, err error)
		// Delete 删除
		Delete(ctx context.Context, in *sysin.DictTypeDeleteInp) (err error)
		// Edit 修改/新增
		Edit(ctx context.Context, in *sysin.DictTypeEditInp) (err error)
		// TreeSelect 获取类型关系树选项
		TreeSelect(ctx context.Context, in *sysin.DictTreeSelectInp) (list []*sysin.DictTypeTree, err error)
		// BuiltinSelect 内置字典选项
		BuiltinSelect() (list []*sysin.DictTypeTree)
	}
	ISysDxBusinessReporting interface {
		// Model 商机报备ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取商机报备列表
		List(ctx context.Context, in *sysin.DxBusinessReportingListInp) (list []*sysin.DxBusinessReportingListModel, totalCount int, err error)
		// Export 导出商机报备
		Export(ctx context.Context, in *sysin.DxBusinessReportingListInp) (err error)
		// Edit 修改/新增商机报备
		Edit(ctx context.Context, in *sysin.DxBusinessReportingEditInp) (err error)
		// Delete 删除商机报备
		Delete(ctx context.Context, in *sysin.DxBusinessReportingDeleteInp) (err error)
		// View 获取商机报备指定信息
		View(ctx context.Context, in *sysin.DxBusinessReportingViewInp) (res *sysin.DxBusinessReportingViewModel, err error)
		// Status 更新商机报备状态
		Status(ctx context.Context, in *sysin.DxBusinessReportingStatusInp) (err error)
		// StatTotalAmount 获取总金额统计
		StatTotalAmount(ctx context.Context, in *sysin.DxBusinessReportingStatTotalAmountInp) (*sysin.DxBusinessReportingStatTotalAmountModel, error)
		// ExportReconciliation 导出对账单
		ExportReconciliation(ctx context.Context, in *sysin.DxBusinessReportingListInp) (err error)
		// Delivery 商机报备交付操作
		Delivery(ctx context.Context, in *sysin.DxBusinessReportingDeliveryInp) (err error)
		// AssociateMember 关联客户
		AssociateMember(ctx context.Context, in *sysin.DxBusinessReportingAssociateMemberInp) (err error)
		// StatusRollback 商机报备状态回退
		StatusRollback(ctx context.Context, in *sysin.DxBusinessReportingStatusRollbackInp) (err error)
	}
	ISysDxSmsAuditLog interface {
		// Model 审核日志ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取审核日志列表
		List(ctx context.Context, in *sysin.DxSmsAuditLogListInp) (list []*sysin.DxSmsAuditLogListModel, totalCount int, err error)
		// Export 导出审核日志
		Export(ctx context.Context, in *sysin.DxSmsAuditLogListInp) (err error)
		// Edit 修改/新增审核日志
		Edit(ctx context.Context, in *sysin.DxSmsAuditLogEditInp) (err error)
		// View 获取审核日志指定信息
		View(ctx context.Context, in *sysin.DxSmsAuditLogViewInp) (res *sysin.DxSmsAuditLogViewModel, err error)
		// ViewByPkId 获取审核日志指定信息
		ViewByPkId(ctx context.Context, in *sysin.DxSmsAuditLogViewByPkIdInp) (res *sysin.DxSmsAuditLogViewModel, err error)
		// ListByPkId 获取审核日志列表
		ListByPkId(ctx context.Context, in *sysin.DxSmsAuditLogListByPkIdInp) (list []*sysin.DxSmsAuditLogListModel, totalCount int, err error)
	}
	ISysDxSmsChargeRecord interface {
		// Model 充值记录ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取充值记录列表
		List(ctx context.Context, in *sysin.DxSmsChargeRecordListInp) (list []*sysin.DxSmsChargeRecordListModel, totalCount int, err error)
		// Export 导出充值记录
		Export(ctx context.Context, in *sysin.DxSmsChargeRecordListInp) (err error)
		// Edit 修改/新增充值记录
		Edit(ctx context.Context, in *sysin.DxSmsChargeRecordEditInp) (err error)
		// Delete 删除充值记录
		Delete(ctx context.Context, in *sysin.DxSmsChargeRecordDeleteInp) (err error)
		// View 获取充值记录指定信息
		View(ctx context.Context, in *sysin.DxSmsChargeRecordViewInp) (res *sysin.DxSmsChargeRecordViewModel, err error)
	}
	ISysDxSmsGroupMobile interface {
		// Model 群发号码ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取群发号码列表
		List(ctx context.Context, in *sysin.DxSmsGroupMobileListInp) (list []*sysin.DxSmsGroupMobileListModel, totalCount int, err error)
		// Export 导出群发号码
		Export(ctx context.Context, in *sysin.DxSmsGroupMobileListInp) (err error)
		// Edit 修改/新增群发号码
		Edit(ctx context.Context, in *sysin.DxSmsGroupMobileEditInp) (err error)
		// Delete 删除群发号码
		Delete(ctx context.Context, in *sysin.DxSmsGroupMobileDeleteInp) (err error)
		// View 获取群发号码指定信息
		View(ctx context.Context, in *sysin.DxSmsGroupMobileViewInp) (res *sysin.DxSmsGroupMobileViewModel, err error)
	}
	ISysDxSmsGroupSend interface {
		// Model 短信群发ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取短信群发列表
		List(ctx context.Context, in *sysin.DxSmsGroupSendListInp) (list []*sysin.DxSmsGroupSendListModel, totalCount int, err error)
		// Edit 修改/新增短信群发
		Edit(ctx context.Context, in *sysin.DxSmsGroupSendEditInp) (err error)
		// Delete 删除短信群发
		Delete(ctx context.Context, in *sysin.DxSmsGroupSendDeleteInp) (err error)
		// View 获取短信群发指定信息
		View(ctx context.Context, in *sysin.DxSmsGroupSendViewInp) (res *sysin.DxSmsGroupSendViewModel, err error)
		// CheckFile 检查短信群发文件
		CheckFile(ctx context.Context, in *sysin.DxSmsGroupSendCheckFileInp) (res *sysin.DxSmsGroupSendCheckFileModel, err error)
		// Cancel 取消发送
		Cancel(ctx context.Context, in *sysin.DxSmsGroupSendCancelInp) (err error)
	}
	ISysDxSmsGroupTemplate interface {
		// Model 模板管理ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取模板管理列表
		List(ctx context.Context, in *sysin.DxSmsGroupTemplateListInp) (list []*sysin.DxSmsGroupTemplateListModel, totalCount int, err error)
		// Export 导出模板管理
		Export(ctx context.Context, in *sysin.DxSmsGroupTemplateListInp) (err error)
		// Edit 修改/新增模板管理
		Edit(ctx context.Context, in *sysin.DxSmsGroupTemplateEditInp) (err error)
		// Delete 删除模板管理
		Delete(ctx context.Context, in *sysin.DxSmsGroupTemplateDeleteInp) (err error)
		// View 获取模板管理指定信息
		View(ctx context.Context, in *sysin.DxSmsGroupTemplateViewInp) (res *sysin.DxSmsGroupTemplateViewModel, err error)
		// Status 更新模板管理状态
		Status(ctx context.Context, in *sysin.DxSmsGroupTemplateStatusInp) (err error)
		// Option 获取模板选项列表
		Option(ctx context.Context, in *sysin.DxSmsGroupTemplateOptionInp) (options []*sysin.DxSmsGroupTemplateOption, err error)
		// DownloadExcel 下载Excel
		DownloadExcel(ctx context.Context, in *sysin.DxSmsGroupTemplateViewInp) (err error)
	}
	ISysDxSmsMoRecord interface {
		// Model 上行记录ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取上行记录列表
		List(ctx context.Context, in *sysin.DxSmsMoRecordListInp) (list []*sysin.DxSmsMoRecordListModel, totalCount int, err error)
		// Export 导出上行记录
		Export(ctx context.Context, in *sysin.DxSmsMoRecordListInp) (err error)
		// View 获取上行记录指定信息
		View(ctx context.Context, in *sysin.DxSmsMoRecordViewInp) (res *sysin.DxSmsMoRecordViewModel, err error)
	}
	ISysDxSmsSendDailyStats interface {
		// Model 短信报表ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取短信报表列表
		List(ctx context.Context, in *sysin.DxSmsSendDailyStatsListInp) (list []*sysin.DxSmsSendDailyStatsListModel, totalCount int, err error)
		// Export 导出短信报表
		Export(ctx context.Context, in *sysin.DxSmsSendDailyStatsListInp) (err error)
		// Summary 获取短信报表统计汇总
		Summary(ctx context.Context, in *sysin.DxSmsSendDailyStatsSummaryInp) (result *sysin.DxSmsSendDailyStatsSummaryModel, err error)
	}
	ISysDxSmsSendRecord interface {
		// Model 短信记录ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取短信记录列表
		List(ctx context.Context, in *sysin.DxSmsSendRecordListInp) (list []*sysin.DxSmsSendRecordListModel, totalCount int, err error)
		// Export 导出短信记录
		Export(ctx context.Context, in *sysin.DxSmsSendRecordListInp) (err error)
		// View 获取短信记录指定信息
		View(ctx context.Context, in *sysin.DxSmsSendRecordViewInp) (res *sysin.DxSmsSendRecordViewModel, err error)
		// ResendFailed 重发失败的短信记录
		ResendFailed(ctx context.Context, in *sysin.DxSmsSendRecordResendFailedInp) (res *sysin.DxSmsSendRecordResendFailedModel, err error)
		// FailureLogList 获取失败重发明细列表
		FailureLogList(ctx context.Context, in *sysin.DxSmsFailureLogListInp) (list []*sysin.DxSmsFailureLogListModel, totalCount int, err error)
		// StatTotalBilling 获取短信计费条数统计
		StatTotalBilling(ctx context.Context, in *sysin.DxSmsSendRecordListInp) (*sysin.DxSmsSendRecordStatTotalBillingModel, error)
	}
	ISysDxSmsSign interface {
		// Model 短信签名ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取短信签名列表
		List(ctx context.Context, in *sysin.DxSmsSignListInp) (list []*sysin.DxSmsSignListModel, totalCount int, err error)
		// Export 导出短信签名
		Export(ctx context.Context, in *sysin.DxSmsSignListInp) (err error)
		// Edit 修改/新增短信签名
		Edit(ctx context.Context, in *sysin.DxSmsSignEditInp) (err error)
		// Delete 删除短信签名
		Delete(ctx context.Context, in *sysin.DxSmsSignDeleteInp) (err error)
		// View 获取短信签名指定信息
		View(ctx context.Context, in *sysin.DxSmsSignViewInp) (res *sysin.DxSmsSignViewModel, err error)
		// Status 更新短信签名状态
		Status(ctx context.Context, in *sysin.DxSmsSignStatusInp) (err error)
		// Option 获取模板选项列表
		Option(ctx context.Context, in *sysin.DxSmsSignOptionInp) (options []*sysin.DxSmsSignOption, err error)
	}
	ISysEmsLog interface {
		// Delete 删除
		Delete(ctx context.Context, in *sysin.EmsLogDeleteInp) (err error)
		// Edit 修改/新增
		Edit(ctx context.Context, in *sysin.EmsLogEditInp) (err error)
		// Status 更新部门状态
		Status(ctx context.Context, in *sysin.EmsLogStatusInp) (err error)
		// View 获取指定字典类型信息
		View(ctx context.Context, in *sysin.EmsLogViewInp) (res *sysin.EmsLogViewModel, err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.EmsLogListInp) (list []*sysin.EmsLogListModel, totalCount int, err error)
		// Send 发送邮件
		Send(ctx context.Context, in *sysin.SendEmsInp) (err error)
		// GetTemplate 获取指定邮件模板
		GetTemplate(ctx context.Context, template string, config *model.EmailConfig) (val string, err error)
		// AllowSend 是否允许发送
		AllowSend(ctx context.Context, models *entity.SysEmsLog, config *model.EmailConfig) (err error)
		// NowDayIpSendCount 当天IP累计发送次数
		NowDayIpSendCount(ctx context.Context, event string) (count int, err error)
		// VerifyCode 效验验证码
		VerifyCode(ctx context.Context, in *sysin.VerifyEmsCodeInp) (err error)
	}
	ISysGenCodes interface {
		// Delete 删除
		Delete(ctx context.Context, in *sysin.GenCodesDeleteInp) (err error)
		// Edit 修改/新增
		Edit(ctx context.Context, in *sysin.GenCodesEditInp) (res *sysin.GenCodesEditModel, err error)
		// Status 更新部门状态
		Status(ctx context.Context, in *sysin.GenCodesStatusInp) (err error)
		// MaxSort 最大排序
		MaxSort(ctx context.Context, in *sysin.GenCodesMaxSortInp) (res *sysin.GenCodesMaxSortModel, err error)
		// View 获取指定字典类型信息
		View(ctx context.Context, in *sysin.GenCodesViewInp) (res *sysin.GenCodesViewModel, err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.GenCodesListInp) (list []*sysin.GenCodesListModel, totalCount int, err error)
		// Selects 选项
		Selects(ctx context.Context, in *sysin.GenCodesSelectsInp) (res *sysin.GenCodesSelectsModel, err error)
		// TableSelect 表选项
		TableSelect(ctx context.Context, in *sysin.GenCodesTableSelectInp) (res []*sysin.GenCodesTableSelectModel, err error)
		// ColumnSelect 表字段选项
		ColumnSelect(ctx context.Context, in *sysin.GenCodesColumnSelectInp) (res []*sysin.GenCodesColumnSelectModel, err error)
		// ColumnList 表字段列表
		ColumnList(ctx context.Context, in *sysin.GenCodesColumnListInp) (res []*sysin.GenCodesColumnListModel, err error)
		// Preview 生成预览
		Preview(ctx context.Context, in *sysin.GenCodesPreviewInp) (res *sysin.GenCodesPreviewModel, err error)
		// Build 提交生成
		Build(ctx context.Context, in *sysin.GenCodesBuildInp) (err error)
	}
	ISysLog interface {
		// Model 请求日志Orm模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// Export 导出
		Export(ctx context.Context, in *sysin.LogListInp) (err error)
		// RealWrite 真实写入
		RealWrite(ctx context.Context, log entity.SysLog) (err error)
		// AutoLog 根据配置自动记录请求日志
		AutoLog(ctx context.Context) error
		// AnalysisLog 解析日志数据
		AnalysisLog(ctx context.Context) entity.SysLog
		// SimplifyHeaderParams 过滤掉请求头中的大参数
		SimplifyHeaderParams(data *gjson.Json) *gjson.Json
		// View 获取指定请求日志信息
		View(ctx context.Context, in *sysin.LogViewInp) (res *sysin.LogViewModel, err error)
		// Delete 删除请求日志
		Delete(ctx context.Context, in *sysin.LogDeleteInp) (err error)
		// List 请求日志列表
		List(ctx context.Context, in *sysin.LogListInp) (list []*sysin.LogListModel, totalCount int, err error)
	}
	ISysLoginLog interface {
		// Model 登录日志Orm模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取登录日志列表
		List(ctx context.Context, in *sysin.LoginLogListInp) (list []*sysin.LoginLogListModel, totalCount int, err error)
		// Export 导出登录日志
		Export(ctx context.Context, in *sysin.LoginLogListInp) (err error)
		// Delete 删除登录日志
		Delete(ctx context.Context, in *sysin.LoginLogDeleteInp) (err error)
		// Push 推送登录日志
		Push(ctx context.Context, in *sysin.LoginLogPushInp)
		// RealWrite 真实写入
		RealWrite(ctx context.Context, models entity.SysLoginLog) (err error)
	}
	ISysNormalTreeDemo interface {
		// Model 普通树表ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取普通树表列表
		List(ctx context.Context, in *sysin.NormalTreeDemoListInp) (list []*sysin.NormalTreeDemoListModel, totalCount int, err error)
		// Edit 修改/新增普通树表
		Edit(ctx context.Context, in *sysin.NormalTreeDemoEditInp) (err error)
		// Delete 删除普通树表
		Delete(ctx context.Context, in *sysin.NormalTreeDemoDeleteInp) (err error)
		// MaxSort 获取普通树表最大排序
		MaxSort(ctx context.Context, in *sysin.NormalTreeDemoMaxSortInp) (res *sysin.NormalTreeDemoMaxSortModel, err error)
		// View 获取普通树表指定信息
		View(ctx context.Context, in *sysin.NormalTreeDemoViewInp) (res *sysin.NormalTreeDemoViewModel, err error)
		// TreeOption 获取普通树表关系树选项
		TreeOption(ctx context.Context) (nodes []tree.Node, err error)
	}
	ISysOptionTreeDemo interface {
		// Model 选项树表ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取选项树表列表
		List(ctx context.Context, in *sysin.OptionTreeDemoListInp) (list []*sysin.OptionTreeDemoListModel, totalCount int, err error)
		// Edit 修改/新增选项树表
		Edit(ctx context.Context, in *sysin.OptionTreeDemoEditInp) (err error)
		// Delete 删除选项树表
		Delete(ctx context.Context, in *sysin.OptionTreeDemoDeleteInp) (err error)
		// MaxSort 获取选项树表最大排序
		MaxSort(ctx context.Context, in *sysin.OptionTreeDemoMaxSortInp) (res *sysin.OptionTreeDemoMaxSortModel, err error)
		// View 获取选项树表指定信息
		View(ctx context.Context, in *sysin.OptionTreeDemoViewInp) (res *sysin.OptionTreeDemoViewModel, err error)
		// TreeOption 获取选项树表关系树选项
		TreeOption(ctx context.Context) (nodes []tree.Node, err error)
	}
	ISysProvinces interface {
		// Tree 关系树选项列表
		Tree(ctx context.Context) (list []*sysin.ProvincesTree, err error)
		// Delete 删除省市区数据
		Delete(ctx context.Context, in *sysin.ProvincesDeleteInp) (err error)
		// Edit 修改/新增省市区数据
		Edit(ctx context.Context, in *sysin.ProvincesEditInp) (err error)
		// Status 更新省市区状态
		Status(ctx context.Context, in *sysin.ProvincesStatusInp) (err error)
		// MaxSort 最大排序
		MaxSort(ctx context.Context, in *sysin.ProvincesMaxSortInp) (res *sysin.ProvincesMaxSortModel, err error)
		// View 获取省市区信息
		View(ctx context.Context, in *sysin.ProvincesViewInp) (res *sysin.ProvincesViewModel, err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.ProvincesListInp) (list []*sysin.ProvincesListModel, totalCount int, err error)
		// ChildrenList 获取省市区下级列表
		ChildrenList(ctx context.Context, in *sysin.ProvincesChildrenListInp) (list []*sysin.ProvincesChildrenListModel, totalCount int, err error)
		// UniqueId 获取省市区下级列表
		UniqueId(ctx context.Context, in *sysin.ProvincesUniqueIdInp) (res *sysin.ProvincesUniqueIdModel, err error)
		// Select 省市区选项
		Select(ctx context.Context, in *sysin.ProvincesSelectInp) (res *sysin.ProvincesSelectModel, err error)
	}
	ISysServeLicense interface {
		// Model 服务许可证ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取服务许可证列表
		List(ctx context.Context, in *sysin.ServeLicenseListInp) (list []*sysin.ServeLicenseListModel, totalCount int, err error)
		// Export 导出服务许可证
		Export(ctx context.Context, in *sysin.ServeLicenseListInp) (err error)
		// Edit 修改/新增服务许可证
		Edit(ctx context.Context, in *sysin.ServeLicenseEditInp) (err error)
		// Delete 删除服务许可证
		Delete(ctx context.Context, in *sysin.ServeLicenseDeleteInp) (err error)
		// View 获取服务许可证指定信息
		View(ctx context.Context, in *sysin.ServeLicenseViewInp) (res *sysin.ServeLicenseViewModel, err error)
		// Status 更新服务许可证状态
		Status(ctx context.Context, in *sysin.ServeLicenseStatusInp) (err error)
		// AssignRouter 分配服务许可证路由
		AssignRouter(ctx context.Context, in *sysin.ServeLicenseAssignRouterInp) (err error)
	}
	ISysServeLog interface {
		// Model 服务日志Orm模型
		Model(ctx context.Context) *gdb.Model
		// List 获取服务日志列表
		List(ctx context.Context, in *sysin.ServeLogListInp) (list []*sysin.ServeLogListModel, totalCount int, err error)
		// Export 导出服务日志
		Export(ctx context.Context, in *sysin.ServeLogListInp) (err error)
		// Delete 删除服务日志
		Delete(ctx context.Context, in *sysin.ServeLogDeleteInp) (err error)
		// View 获取服务日志指定信息
		View(ctx context.Context, in *sysin.ServeLogViewInp) (res *sysin.ServeLogViewModel, err error)
		// RealWrite 真实写入
		RealWrite(ctx context.Context, models entity.SysServeLog) (err error)
	}
	ISysSmsChannel interface {
		// Model 短信通道ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取短信通道列表
		List(ctx context.Context, in *sysin.SmsChannelListInp) (list []*sysin.SmsChannelListModel, totalCount int, err error)
		// Export 导出短信通道
		Export(ctx context.Context, in *sysin.SmsChannelListInp) (err error)
		// Edit 修改/新增短信通道
		Edit(ctx context.Context, in *sysin.SmsChannelEditInp) (err error)
		// Delete 删除短信通道
		Delete(ctx context.Context, in *sysin.SmsChannelDeleteInp) (err error)
		// MaxSort 获取短信通道最大排序
		MaxSort(ctx context.Context, in *sysin.SmsChannelMaxSortInp) (res *sysin.SmsChannelMaxSortModel, err error)
		// View 获取短信通道指定信息
		View(ctx context.Context, in *sysin.SmsChannelViewInp) (res *sysin.SmsChannelViewModel, err error)
		// Status 更新短信通道状态
		Status(ctx context.Context, in *sysin.SmsChannelStatusInp) (err error)
		// TreeOption 获取短信通道关系树选项
		TreeOption(ctx context.Context) (nodes []tree.Node, err error)
		// Option 获取短信通道选项
		Option(ctx context.Context) (opts []*model.Option, err error)
	}
	ISysSmsLog interface {
		// Delete 删除
		Delete(ctx context.Context, in *sysin.SmsLogDeleteInp) (err error)
		// View 获取指定字典类型信息
		View(ctx context.Context, in *sysin.SmsLogViewInp) (res *sysin.SmsLogViewModel, err error)
		// List 获取列表
		List(ctx context.Context, in *sysin.SmsLogListInp) (list []*sysin.SmsLogListModel, totalCount int, err error)
		// SendCode 发送验证码
		SendCode(ctx context.Context, in *sysin.SendCodeInp) (err error)
		// GetTemplate 获取指定短信模板
		GetTemplate(ctx context.Context, template string, config *model.SmsConfig) (val string, err error)
		// AllowSend 是否允许发送
		AllowSend(ctx context.Context, models *entity.SysSmsLog, config *model.SmsConfig) (err error)
		// NowDayIpSendCount 当天IP累计发送次数
		NowDayIpSendCount(ctx context.Context, event string) (count int, err error)
		// VerifyCode 效验验证码
		VerifyCode(ctx context.Context, in *sysin.VerifyCodeInp) (err error)
	}
	ISysNotifyConfig interface {
		// Model 系统_审核通知配置ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取系统_审核通知配置列表
		List(ctx context.Context, in *sysin.SysNotifyConfigListInp) (list []*sysin.SysNotifyConfigListModel, totalCount int, err error)
		// Export 导出系统_审核通知配置
		Export(ctx context.Context, in *sysin.SysNotifyConfigListInp) (err error)
		// Edit 修改/新增系统_审核通知配置
		Edit(ctx context.Context, in *sysin.SysNotifyConfigEditInp) (err error)
		// Delete 删除系统_审核通知配置
		Delete(ctx context.Context, in *sysin.SysNotifyConfigDeleteInp) (err error)
		// View 获取系统_审核通知配置指定信息
		View(ctx context.Context, in *sysin.SysNotifyConfigViewInp) (res *sysin.SysNotifyConfigViewModel, err error)
		// Status 更新系统_审核通知配置状态
		Status(ctx context.Context, in *sysin.SysNotifyConfigStatusInp) (err error)
		// SendAuditNotify 发送审核通知
		SendAuditNotify(ctx context.Context, category int) (err error)
	}
	ISysTestCategory interface {
		// Model 测试分类ORM模型
		Model(ctx context.Context, option ...*handler.Option) *gdb.Model
		// List 获取测试分类列表
		List(ctx context.Context, in *sysin.TestCategoryListInp) (list []*sysin.TestCategoryListModel, totalCount int, err error)
		// Edit 修改/新增测试分类
		Edit(ctx context.Context, in *sysin.TestCategoryEditInp) (err error)
		// Delete 删除测试分类
		Delete(ctx context.Context, in *sysin.TestCategoryDeleteInp) (err error)
		// MaxSort 获取测试分类最大排序
		MaxSort(ctx context.Context, in *sysin.TestCategoryMaxSortInp) (res *sysin.TestCategoryMaxSortModel, err error)
		// View 获取测试分类指定信息
		View(ctx context.Context, in *sysin.TestCategoryViewInp) (res *sysin.TestCategoryViewModel, err error)
		// Status 更新测试分类状态
		Status(ctx context.Context, in *sysin.TestCategoryStatusInp) (err error)
		// Option 获取测试分类选项
		Option(ctx context.Context) (opts []*model.Option, err error)
	}
)

var (
	localSysAddons              ISysAddons
	localSysAddonsConfig        ISysAddonsConfig
	localSysAdminCompany        ISysAdminCompany
	localSysAttachment          ISysAttachment
	localSysBlacklist           ISysBlacklist
	localSysCompanyAccount      ISysCompanyAccount
	localSysCompanySubAccount   ISysCompanySubAccount
	localSysConfig              ISysConfig
	localSysContact             ISysContact
	localSysContactGroup        ISysContactGroup
	localSysCron                ISysCron
	localSysCronGroup           ISysCronGroup
	localSysCurdDemo            ISysCurdDemo
	localSysDictData            ISysDictData
	localSysDictType            ISysDictType
	localSysDxBusinessReporting ISysDxBusinessReporting
	localSysDxSmsAuditLog       ISysDxSmsAuditLog
	localSysDxSmsChargeRecord   ISysDxSmsChargeRecord
	localSysDxSmsGroupMobile    ISysDxSmsGroupMobile
	localSysDxSmsGroupSend      ISysDxSmsGroupSend
	localSysDxSmsGroupTemplate  ISysDxSmsGroupTemplate
	localSysDxSmsMoRecord       ISysDxSmsMoRecord
	localSysDxSmsSendDailyStats ISysDxSmsSendDailyStats
	localSysDxSmsSendRecord     ISysDxSmsSendRecord
	localSysDxSmsSign           ISysDxSmsSign
	localSysEmsLog              ISysEmsLog
	localSysGenCodes            ISysGenCodes
	localSysLog                 ISysLog
	localSysLoginLog            ISysLoginLog
	localSysNormalTreeDemo      ISysNormalTreeDemo
	localSysOptionTreeDemo      ISysOptionTreeDemo
	localSysProvinces           ISysProvinces
	localSysServeLicense        ISysServeLicense
	localSysServeLog            ISysServeLog
	localSysSmsChannel          ISysSmsChannel
	localSysSmsLog              ISysSmsLog
	localSysNotifyConfig        ISysNotifyConfig
	localSysTestCategory        ISysTestCategory
)

func SysAddons() ISysAddons {
	if localSysAddons == nil {
		panic("implement not found for interface ISysAddons, forgot register?")
	}
	return localSysAddons
}

func RegisterSysAddons(i ISysAddons) {
	localSysAddons = i
}

func SysAddonsConfig() ISysAddonsConfig {
	if localSysAddonsConfig == nil {
		panic("implement not found for interface ISysAddonsConfig, forgot register?")
	}
	return localSysAddonsConfig
}

func RegisterSysAddonsConfig(i ISysAddonsConfig) {
	localSysAddonsConfig = i
}

func SysAdminCompany() ISysAdminCompany {
	if localSysAdminCompany == nil {
		panic("implement not found for interface ISysAdminCompany, forgot register?")
	}
	return localSysAdminCompany
}

func RegisterSysAdminCompany(i ISysAdminCompany) {
	localSysAdminCompany = i
}

func SysAttachment() ISysAttachment {
	if localSysAttachment == nil {
		panic("implement not found for interface ISysAttachment, forgot register?")
	}
	return localSysAttachment
}

func RegisterSysAttachment(i ISysAttachment) {
	localSysAttachment = i
}

func SysBlacklist() ISysBlacklist {
	if localSysBlacklist == nil {
		panic("implement not found for interface ISysBlacklist, forgot register?")
	}
	return localSysBlacklist
}

func RegisterSysBlacklist(i ISysBlacklist) {
	localSysBlacklist = i
}

func SysCompanyAccount() ISysCompanyAccount {
	if localSysCompanyAccount == nil {
		panic("implement not found for interface ISysCompanyAccount, forgot register?")
	}
	return localSysCompanyAccount
}

func RegisterSysCompanyAccount(i ISysCompanyAccount) {
	localSysCompanyAccount = i
}

func SysCompanySubAccount() ISysCompanySubAccount {
	if localSysCompanySubAccount == nil {
		panic("implement not found for interface ISysCompanySubAccount, forgot register?")
	}
	return localSysCompanySubAccount
}

func RegisterSysCompanySubAccount(i ISysCompanySubAccount) {
	localSysCompanySubAccount = i
}

func SysConfig() ISysConfig {
	if localSysConfig == nil {
		panic("implement not found for interface ISysConfig, forgot register?")
	}
	return localSysConfig
}

func RegisterSysConfig(i ISysConfig) {
	localSysConfig = i
}

func SysContact() ISysContact {
	if localSysContact == nil {
		panic("implement not found for interface ISysContact, forgot register?")
	}
	return localSysContact
}

func RegisterSysContact(i ISysContact) {
	localSysContact = i
}

func SysContactGroup() ISysContactGroup {
	if localSysContactGroup == nil {
		panic("implement not found for interface ISysContactGroup, forgot register?")
	}
	return localSysContactGroup
}

func RegisterSysContactGroup(i ISysContactGroup) {
	localSysContactGroup = i
}

func SysCron() ISysCron {
	if localSysCron == nil {
		panic("implement not found for interface ISysCron, forgot register?")
	}
	return localSysCron
}

func RegisterSysCron(i ISysCron) {
	localSysCron = i
}

func SysCronGroup() ISysCronGroup {
	if localSysCronGroup == nil {
		panic("implement not found for interface ISysCronGroup, forgot register?")
	}
	return localSysCronGroup
}

func RegisterSysCronGroup(i ISysCronGroup) {
	localSysCronGroup = i
}

func SysCurdDemo() ISysCurdDemo {
	if localSysCurdDemo == nil {
		panic("implement not found for interface ISysCurdDemo, forgot register?")
	}
	return localSysCurdDemo
}

func RegisterSysCurdDemo(i ISysCurdDemo) {
	localSysCurdDemo = i
}

func SysDictData() ISysDictData {
	if localSysDictData == nil {
		panic("implement not found for interface ISysDictData, forgot register?")
	}
	return localSysDictData
}

func RegisterSysDictData(i ISysDictData) {
	localSysDictData = i
}

func SysDictType() ISysDictType {
	if localSysDictType == nil {
		panic("implement not found for interface ISysDictType, forgot register?")
	}
	return localSysDictType
}

func RegisterSysDictType(i ISysDictType) {
	localSysDictType = i
}

func SysDxBusinessReporting() ISysDxBusinessReporting {
	if localSysDxBusinessReporting == nil {
		panic("implement not found for interface ISysDxBusinessReporting, forgot register?")
	}
	return localSysDxBusinessReporting
}

func RegisterSysDxBusinessReporting(i ISysDxBusinessReporting) {
	localSysDxBusinessReporting = i
}

func SysDxSmsAuditLog() ISysDxSmsAuditLog {
	if localSysDxSmsAuditLog == nil {
		panic("implement not found for interface ISysDxSmsAuditLog, forgot register?")
	}
	return localSysDxSmsAuditLog
}

func RegisterSysDxSmsAuditLog(i ISysDxSmsAuditLog) {
	localSysDxSmsAuditLog = i
}

func SysDxSmsChargeRecord() ISysDxSmsChargeRecord {
	if localSysDxSmsChargeRecord == nil {
		panic("implement not found for interface ISysDxSmsChargeRecord, forgot register?")
	}
	return localSysDxSmsChargeRecord
}

func RegisterSysDxSmsChargeRecord(i ISysDxSmsChargeRecord) {
	localSysDxSmsChargeRecord = i
}

func SysDxSmsGroupMobile() ISysDxSmsGroupMobile {
	if localSysDxSmsGroupMobile == nil {
		panic("implement not found for interface ISysDxSmsGroupMobile, forgot register?")
	}
	return localSysDxSmsGroupMobile
}

func RegisterSysDxSmsGroupMobile(i ISysDxSmsGroupMobile) {
	localSysDxSmsGroupMobile = i
}

func SysDxSmsGroupSend() ISysDxSmsGroupSend {
	if localSysDxSmsGroupSend == nil {
		panic("implement not found for interface ISysDxSmsGroupSend, forgot register?")
	}
	return localSysDxSmsGroupSend
}

func RegisterSysDxSmsGroupSend(i ISysDxSmsGroupSend) {
	localSysDxSmsGroupSend = i
}

func SysDxSmsGroupTemplate() ISysDxSmsGroupTemplate {
	if localSysDxSmsGroupTemplate == nil {
		panic("implement not found for interface ISysDxSmsGroupTemplate, forgot register?")
	}
	return localSysDxSmsGroupTemplate
}

func RegisterSysDxSmsGroupTemplate(i ISysDxSmsGroupTemplate) {
	localSysDxSmsGroupTemplate = i
}

func SysDxSmsMoRecord() ISysDxSmsMoRecord {
	if localSysDxSmsMoRecord == nil {
		panic("implement not found for interface ISysDxSmsMoRecord, forgot register?")
	}
	return localSysDxSmsMoRecord
}

func RegisterSysDxSmsMoRecord(i ISysDxSmsMoRecord) {
	localSysDxSmsMoRecord = i
}

func SysDxSmsSendDailyStats() ISysDxSmsSendDailyStats {
	if localSysDxSmsSendDailyStats == nil {
		panic("implement not found for interface ISysDxSmsSendDailyStats, forgot register?")
	}
	return localSysDxSmsSendDailyStats
}

func RegisterSysDxSmsSendDailyStats(i ISysDxSmsSendDailyStats) {
	localSysDxSmsSendDailyStats = i
}

func SysDxSmsSendRecord() ISysDxSmsSendRecord {
	if localSysDxSmsSendRecord == nil {
		panic("implement not found for interface ISysDxSmsSendRecord, forgot register?")
	}
	return localSysDxSmsSendRecord
}

func RegisterSysDxSmsSendRecord(i ISysDxSmsSendRecord) {
	localSysDxSmsSendRecord = i
}

func SysDxSmsSign() ISysDxSmsSign {
	if localSysDxSmsSign == nil {
		panic("implement not found for interface ISysDxSmsSign, forgot register?")
	}
	return localSysDxSmsSign
}

func RegisterSysDxSmsSign(i ISysDxSmsSign) {
	localSysDxSmsSign = i
}

func SysEmsLog() ISysEmsLog {
	if localSysEmsLog == nil {
		panic("implement not found for interface ISysEmsLog, forgot register?")
	}
	return localSysEmsLog
}

func RegisterSysEmsLog(i ISysEmsLog) {
	localSysEmsLog = i
}

func SysGenCodes() ISysGenCodes {
	if localSysGenCodes == nil {
		panic("implement not found for interface ISysGenCodes, forgot register?")
	}
	return localSysGenCodes
}

func RegisterSysGenCodes(i ISysGenCodes) {
	localSysGenCodes = i
}

func SysLog() ISysLog {
	if localSysLog == nil {
		panic("implement not found for interface ISysLog, forgot register?")
	}
	return localSysLog
}

func RegisterSysLog(i ISysLog) {
	localSysLog = i
}

func SysLoginLog() ISysLoginLog {
	if localSysLoginLog == nil {
		panic("implement not found for interface ISysLoginLog, forgot register?")
	}
	return localSysLoginLog
}

func RegisterSysLoginLog(i ISysLoginLog) {
	localSysLoginLog = i
}

func SysNormalTreeDemo() ISysNormalTreeDemo {
	if localSysNormalTreeDemo == nil {
		panic("implement not found for interface ISysNormalTreeDemo, forgot register?")
	}
	return localSysNormalTreeDemo
}

func RegisterSysNormalTreeDemo(i ISysNormalTreeDemo) {
	localSysNormalTreeDemo = i
}

func SysOptionTreeDemo() ISysOptionTreeDemo {
	if localSysOptionTreeDemo == nil {
		panic("implement not found for interface ISysOptionTreeDemo, forgot register?")
	}
	return localSysOptionTreeDemo
}

func RegisterSysOptionTreeDemo(i ISysOptionTreeDemo) {
	localSysOptionTreeDemo = i
}

func SysProvinces() ISysProvinces {
	if localSysProvinces == nil {
		panic("implement not found for interface ISysProvinces, forgot register?")
	}
	return localSysProvinces
}

func RegisterSysProvinces(i ISysProvinces) {
	localSysProvinces = i
}

func SysServeLicense() ISysServeLicense {
	if localSysServeLicense == nil {
		panic("implement not found for interface ISysServeLicense, forgot register?")
	}
	return localSysServeLicense
}

func RegisterSysServeLicense(i ISysServeLicense) {
	localSysServeLicense = i
}

func SysServeLog() ISysServeLog {
	if localSysServeLog == nil {
		panic("implement not found for interface ISysServeLog, forgot register?")
	}
	return localSysServeLog
}

func RegisterSysServeLog(i ISysServeLog) {
	localSysServeLog = i
}

func SysSmsChannel() ISysSmsChannel {
	if localSysSmsChannel == nil {
		panic("implement not found for interface ISysSmsChannel, forgot register?")
	}
	return localSysSmsChannel
}

func RegisterSysSmsChannel(i ISysSmsChannel) {
	localSysSmsChannel = i
}

func SysSmsLog() ISysSmsLog {
	if localSysSmsLog == nil {
		panic("implement not found for interface ISysSmsLog, forgot register?")
	}
	return localSysSmsLog
}

func RegisterSysSmsLog(i ISysSmsLog) {
	localSysSmsLog = i
}

func SysNotifyConfig() ISysNotifyConfig {
	if localSysNotifyConfig == nil {
		panic("implement not found for interface ISysNotifyConfig, forgot register?")
	}
	return localSysNotifyConfig
}

func RegisterSysNotifyConfig(i ISysNotifyConfig) {
	localSysNotifyConfig = i
}

func SysTestCategory() ISysTestCategory {
	if localSysTestCategory == nil {
		panic("implement not found for interface ISysTestCategory, forgot register?")
	}
	return localSysTestCategory
}

func RegisterSysTestCategory(i ISysTestCategory) {
	localSysTestCategory = i
}
