package packed

import "github.com/gogf/gf/v2/os/gres"

func init() {
	if err := gres.Add("H4sIAAAAAAAC/7RaCTjU2xs+FNmXkMouY2co11IoUo19F0JNjDGMmcEQIS0kyVIppCylBQlpQ0pFkSVLpQXZKYQWa/g//e/NnZ8YM6PreZp51NP7vt/3nd/5fed8r5nhsuW8gAkwgRgE0Q6Q/KwFzICI8iRgkUSUgiceh1dAEghwY6QHyhWDRVlbMQA64StaaJYvo5wR+4bZLMzMzHKtzeqkaxDVFebS0qZ1nV98vvmycxCIfgyGMpfTr8DOdsdJexXcYxwb2/H0SLpOOyyjgEEjApZRWDAeMF4YwcBg4KA4XdNyQ2fMioDqhvXEXUmX7f2IlpBEH2Fu7jxvVNyExVrsL3h7EAAwM2NmuIKpUUtrLAAAkAEAWDgSgXkjsdimo2e8TdFYbzYUCxvCi31yBlXG1kz61Qrl+vL61oRKuAVTTZXhC312A4XynnYW+WqjGhsmRX152TyL1nrrcmkZGek79Y2mL0yXYV5Wwg0a4csz6Jb/0qdzSjTMBABgRKIP/KaPb159SAJmVtlPLNL/sTCWwEJYcDcUFotfFPH37EmQR/z7UxH9L/St6Gr3J1vYQl/UWbJpl8GG21f0VnyMSVMx0x08nq1uWJxVvZ2d+fKgU/tI7Qo3hr2tzglregiTjTtq0uqclBBxVZJa6iNM/ZfyYuqdAx8daUFzHO7OCMuI5rPx0MxRu7frdtLxaRQ/7HOLP1c/SvBMeXj6W6WrdnUYFY1dOipyjPjJUc9XGMIRjk3olfXlkpWp5vHnT3V0Nse1mfjqwE46nkoNvn+xca32uU6hfYc5zxkTWt6u2Sxk/TWb1UWjtdtq5+mubRywbrvoh3VedBXjE+cMJ9JzZtebxfiBlkYAQA4duRoIL5IxP2UayiC1KOjvlcBW55kcUWZb3lpwgKWSRz5qTxUPEYl0zhr3On8sIbQ0w16sRchcMj1aqrm5MSAQzaepBXj7PNVbHkXf8nfKmcy2q70i48Zy9VksY9Jy2L0VQ4b8mojsATttrwnbpA7+fuKMRI1/f7+GRflDdl30hZMcIY9KfQIj09tDfRKH7+ntx7BarmkYLtT5HBY9NMV5Tk1UTvKkQ43wzV2wuA27clZiJqRF9muNM/9K7kp7QWwmAKCH7AJfNW8e3JDOHjQkVXxBMLgzHueKQSsGID2xs8B60cUmcsq8oZPBx83rm+Ty/KYRsXISshIsLE4leaYn+N4/e4PeOjJgziuROX4uEX8U5tTiJFb1kc6h/oRt60fdCI8r4qneEVGVUa9yS68iBrb3lVx13SCD0wn0cyziDSd4y5yjY7LC0flX7gPb021rGCM1R3Nc1uYnfHctCmwSSU4uexXSV62T/dIgu9qv6N1Mo67jqoeoEst9CjYRDtpYpVjVOiWQ3Jz4ztH7/N4vjJF1j10ffkD2q30kltmKxcltn3hgepx9MMpk2GBqcE+ydZVqbGaDeU/ArdvTsxuW/bZPfC0AAFE6GtP280PBGYtR9Py3HtjY3Vk8ylxlKWop7bmjFvKFERNvdK1Z7jB4bRvWeSfutaHiqOvpm4l3WKaGbk6tuzjq2V116OumdcK5xxN9mT71tbhJEujvhqYmrneeRjKYyCDENiv0xXyQdDvMbzzEY36/uvSa2/EoaRNcgof6JqzCV23TOO/k908436abFHRe97b5zotAP/+0NtVoY1nsLfOaAyFBZ3ex0CkeaueOLJxeP707aDTggaCzytrD5+4YMr4I4SjYuU0W3ZZzxmyqMCM4vE001L/k/mXXFULVIu9Pw7oGZKfPzcgMyGeuczU7Gi0rrFvS97aXrwbtH/l9bVdH0kpriTi4IfZuQ1setwAdL8NfnroFiEHfHfwghZvnQ2L5/S2uzRtGgt2PnYjhmCj5iKvtt4yzOMt/6mLDprXX4EnfGz7Q/arF6ZsujzbQAfCGbC2EydeCtA6h1h9i2lW5DjjN8FlGG+gVc2XEXB8hEtveWN5lfSdyxuSyrsl6Ov5nfgPMfNt+DJ5xvklX2D+852nyiw9qwjFSU3VjlUNd0wHKiRyDSMIyDMyl5s0Kf2UNqdCI6b0BESXWHIcFm1pCGOpFNdLWEUotI3J1+kJtgl+nNBuLPGBPXX99/bLy9F7WrmOGFTe04azX2Eqb8mfaUaJ8x98rWJlcg7nY8+21szPRlZO+rogsy/dd+Xpyl9NzG9FQuxh8+cs+ro7NTu5Ps7e2bn+Jtr2o28d5FrPNxjBC1Wu8EFVTbtAdruYff+1wSvBB/a0qdWZMERwsnkiV85KXMOttJNzen+p2yD/fxxrXMrnWzcYu8niJ7RS/6/LKo9MS+62sP67dnWUJz99/P5WPi78jRLeI+y33JVbW/qGQQ7d1bu9M6sNGrdKriN+B/R73qmON5d1wAUXksOZ50LTlbo+NNEye4VCZMfZTU2neLowhQU04+uKxCN22D7WT/of9SjO8uZ5tf88G42c5MB3XeU/fCobiLRbryr6Wwmr4/lja823DjAje2mYtTqnJmCF3wTvCp2uckj/b308JcHD2T3/xVmxTs2PUh61GLIXFdd9GGoc72pFj79XXEvclB4YEi1311Rg6TqzQd3F4rpDwPPvHe80S+bXBZ8cfhOe+1nxYfSpneMtgR2GEcaxI2sn9gRL9Ipfi3Y/7Ol5OOLncSgB5W9a8zny4schuLLDnHRyl8YzdwPPisbysFxzMe+8f1SiSbSzI5RLoHRG+2tBT1tvnHMd1VtJuZzRRZrqsT6Po+ReeLRVnpGvNV7Zj8iJaUs8E9Z36jFM+MBY8qPZwP6dWV8Omvb6RLEm6quCesjer13Cz47TmxYObg/hOoEcb8GksreECvcTWFx6pfLKc+2Fq52/ylT7AaX7DqN4X2hHuMPlDCm2Ud6gG1+LFmB+Azk3aF7T6QVS4XUSpj4lmlYSpeeGeHroNgyo516cVf3y7FNl9oXjG91MvK7d72T7lDzVRr9IbMsdCRr+0XMm6bb3XwrWedaAsXUqo78S7UQWR/c+FdKd0NqaVFfLnP70nzd00w/DrqZspCskSWwYA9wpyb6H5m2MMjojyxiGxNPRtwmQB4c6eLjS83mCLgv78Q9oxEGN1PJqUuMJe1OesJt484lwnzF4gUTtyyuzOrdEMrdfHDdYpn1I7dHOm2LaoVvCmlDI+uSXo/omvIsTlPg63RpSlMmoxqO7SMy2ZdfZrevq8JrVqZOzjm9yb3U1TBmRXV2z0aSixyt70+hx+l0UeLroyfKdsmWBufbDR+c/K6L6CU0qMRU/0WQ6VCsuEuhBzvjOjpEK2vxgCgf1jcXv1nKbdLzsp1S/fI75f9YjNJ7TOjoZNbeaa2TwJFuay/U1mCBMJz0lUR8Bh346CpD4Bh741KYqDYM3l2BLHoslidi2iBL3EqfRwIV2+BOGSqM32uEY1zA9ZTOWFPNd7nUXBR4tsmv0Ru2CYsx5H3SeH38INU3fyflKsmXms1gmLFbzWWRo2se+hsBbcMcnaAxeU1rg7pfCAm71Pco5teBQ8VNeXh3FER85X+NbGPqcZ1l/L6a/owAFjOgDU6clVX2yRQuFxPkQfGhaAHCW4/3yRLgNZhFGNiZGpNaLmhZyiTDf9bH9Qws9WxQcA4CS7mGGL0hK98VgsypuGB0WOUmyaDzwq1DHMcwCqecRMr8wV1haY6c9+L6PkdK64/61DXkJqR5yTsZKIQv7A2pnpJ/EbRh4qdp0wzGP0iBVuyrb3PV0rcGPZjpokTb7wr8w6G22DD/rWMecXrHvQ1+wgxn5ECO+BMgg5eSVentOOb1tzovZso5BhhjiKBQBwkG0U1GkJazcOtQ8aWrH7ky1cy1vfTfIeidzD/zptBQLWs4Or/+m13YYe4+XNA1523Cdqi8IljIVFk10zY9b0DI19EOup7RVdDdNHnqY7z6g3UvFGag3hQdu2FNYdOl9fP7baGjbFHd7BbDo5YnXos/eJ9aODmiw3Vh5eOZ5we7XqLfuYlhsNBZ/+KjR/FrkhQiO3QtG9XqinXz1wtOP1LdjNXatUzl13Y7IoXCtic6WlO6x3/WXhFeP9n6pH9mW6736YlaXckHnMGNbWlpPMb3/sAf2vpA2dQ6x/CgCIJps0LZqS5qe8+/ezWHQxrk+JLQz9Vcb/LzsdcbFRPnreCxtU8jPrj5vrW7m9UzL71sxaJLSRKMokGNRzNWxMeGXbBW/Xg3kqq8qEN/IUyGa7p27AvJz88UmcP0WdXZv9wPjgCuawoPVDK9Ks7DkMrj4+Y5r2l8i73tGVD8+z2x5E3uCePBbOupOvJrGzCzsc4DxxTW6ajRA/GKKXtkL4bP77k3Ja8iO6DXjMRvEI7LO9w8sKO298S8DUS7V51/my3vlsYNmx8WRRxbU4D7zf5hbbL+83O6fx1NvJBj3eOXsgFn/M/ax+znmB6pebCxL/xw7EpKBwRTSG6IFCEWbRZx+ZnwAMi+41ouQpsHg0xpkG5TIUwP7X2j3xLijsn9f+f9gla19HCYkLLYtGnjLkJUcgRQkPCkfEEANoiEKJcvQlR7JIa0JAOnugXP58a/I37j9f87UmoC5DxuHfm+CvjO6veBZtTMTJk/qgvP0wzqg/H80/wFQW43eaNfPSeCIxONIUBdXoZD5W4no67NvDm5blUsd2X+5+2D1CB4OLUcPqfiPdCwid+0Uvs9unJw/8kAuTu+yWcBO43ECZO6dtStRGFd/K5wxTPqOStv3GBozgPcSaazZVF724z3lZtr9ZRhTJUWmb4qxkt6z93tceLuDf/4jOyPjxprqNXw8qDvVyxpWJJL2p/l50LbBiIifLlyN5iOVXhOu4kj+eAgA0AuoPVp5IHMYV5UOkoV8UIwv4zzUfDRVXoAR33lvEEzGPTJ4osR0dPPmFw/xeCdL4ziPVrtJ1Qyy4DleGuxwRRRF3dwwcLWU+adlvOj01/uj8Nm/2Gthn1hNvCPqK3jub+XbtzHPIxW5MnhDznDhEWKfuSq+TjVNyQ7GV2ua+3nMW/aQiEB+vjT4vyokX7dlz/YimyuCZq2deyaWHdoU3G2adsQmx9XWohbUT4+uP+yfu+osJjpBczap3V2hlwNk96VVjOa1vprVtspjoFawcOtaNKJ2+HuLslqB9OK9j51S6WQIMu2+yJ/7J8DdZxwNNn6Oj8EH57JPFmzU1HsmObPpV7WuHlSbaAAB4uiUUxwVFwOID/tghYQ4u3MPXh4j3xOxH0cCgRB0DfC/Sh5Z9ZQsNNP/8rScKR4QuvKDYYpPlymyhk5pl90tYHJM4Ik61y+4/nV/y2O96FN3A+0tDPu9O3/PRmOhukOD9UtoX+5mPV/mzNkeX8Q+r4ZK04A0j8k/1pQwyGQjrbyDumBk4XQ/cLX0RGLy47rw+IXMmlbtJMtf7mspavnB6Nhvc28aUTPM7jc8u9GWy9/TfLUjSMIVLqKmUfMgNyA48pZ5T9OnLFM5qdUSK5ZYTTjeCnjF4B+3U8jtzx7qpT8rzUty/Qxe2OjnVfABAINlOfSstmfr1K5KIweOgyZJ/nsf+WImLoXb6yhC9skH4fXqtYi/t3sphx04f8bCPMojx9Hg17boLV2ReNof/dYA78mNSiu2gMOau8wFkp52sr36bV7amm6wQLhA+JGFpm5vtp9p7DI3LMnpUx7nqh4z4w9n4ypgqnrr+fFQA9SeRReL7570DjQxbrcOyTJkprK346hD9oY837g0wMNoXxp+Iq5U4flIjRL/X22/zxCNnOfyw+S2eZLWxswe4tMtenGI4t1xE0OjgmwJmJCL/uVgGprHUVcnoy4+k/SfWYGBtwcv09HzYq+2Zd8cEOx6eWpc3U6f6NlZW4yWGTrXI/vDu2XDzL+6pCgQAXCf7fKlQGS7eD+WNRQb40PAoa9JIBXdB+aGweAINj7XhEin/ea94IgnQ0mpGFZuUKbFtH9Usu9pfHMcegNq+d++MnhFz5zMD78hYf389zOfuDh/5hvbIO8Ie3P2jnzecFfW/3qWAM1WJ91UimAmaJVgrqexqr4U1WvCfU8jZcQGDOzuTVKnms/KHOuh95+fpXQW2ur0rEmztQjruDMl0t3cbTuxwbB10G/5S55FYGOL1/pZx8MV94SbfObtPpxdJMmlaFl6IMcvZqvTOuhk/uwoipixeXQEA/CCbJ6Ol5mmhrRAbpZX5ZAtb6KRWmUOJplPWRjQCjuXWs9nsHyZ2l1PgjVWi+5h2QtvLovNbnzCf15i+MTNitZlJrUIYzpPjahFEUP6r5G2o2d63BS75Q2/U86PigidQritWXXj7JPJVl2AkeI/mXO78Hc7wcmy47yhHQfqOB8t+RY5ets55HwDgLtnITZcaObmtrcrYPXQL147h4AsX4Xsiimp3Mtkx3IS/f3ildjo1S5BgspmtY3o0WWVqq1o27OiUSo5dSqtVZabA4O6n9s+eqTxBXXSsXPlkNLv09qqLHFlo01zE1j3Cic/s2FRCX0gYdhh+0XlZ2rC5v7FIXdV4c69Ay6vDfi/lLo0pRLrYng5dveq6t7CJ3rfZvvDH5fIfxwEAWUtp41zwzh4UXCdSeiCcgwvX+/8XxPfizfpl1Lq8wtRI5rVplUx5leHt9a/zW9strii2WtiIAs2pw1sFg4MEt2pOTV3DbYrP2p2gey8k9m4kmuto9UQEA4NDtKIiy7aMsvHL2yoyelmsUjd+f5T57ZuhhQ/rN5/1Xw3k2nl8LKuMvvnwbPjKlblF80aq2eawzhNNP3gZBlhUZDIno/e8kZWWXh6uzhhm1uxqG/tYs7yrn5VFNFeNbvZUcREX050GALCjp/7wMjcHf38p+riR+GVemj6vajA1kK9pbbewYaool7f52/5TLm+DyEYYKyroG5sjDIwr4QbWiArTGhMFC4RsdZV+XQf9si2zW8HJfIvhrf/vdcgtABHyIjHK6jgayi+9OOp/dJsxy0HwxhPxe31daVCvShkyXPGnfAWMqwIO5Yzy8UF6B9Aay/zHM2+UD97X2xn1x45nvwDhBN+9WAru2H7HlaQEF+5G9KTlEgxOMfiSF480RVQErC8aQ8v6V6YCfsmxyFJEtoTFpEgVAdzZh5aZmhrVJEtOnBJ1lBhPJJqWw6YGDTRLjk2BOlJ3nz+2T5Lh+I/2/FmGX//0xxqW35CXHMH8V42+RAyWtjtrcXJ4S1bLPR86GZV09LzL/tVJaiP++Yb79ZN+6OcnBSbouYCkvl8BCODzeQF/9yL/izi/X/jvHy4woONOBxZ1Dy+MJQDBurMQ1m/DdGi8pL5YCUi8TPSAOvcwObHCELEblwHKbbZQvaRWUymI3phFQamTvAoiOX45WNy8CpVK6sgUh0jtXBBs3mvnucCk9kIosAAjoMbeOReY1EElDAG+ugJQ6lUkl9O1kJwKsQLKrFiUriscWcA5Vixo6KRuHxgk9CeLgs61YpETLAYRjGUHVLmHoJpJLT1yEM2llODO4x4ipxwGUb6JA1BtFFoYWw76rFGKvcjeRuquUYHkZ5w6BnIbx99UpJ4UdQhVLSdYunlnLh2peUMLQsfADf6Q7YXyx85wJaDcJAINhLRHgG7nqYuC/t56kJMsCpG8jAdQ4w9ZWLQMRLQ9BbBLkf2QPP5cawilslfxAhqsIeRkr4PIDqQE34WyVSIPUf6KMmTqxEtBxGvxARoMIQsHoAQJIIlydOqCgL5oVqwCVHlBoPJJLRrQF81uSnDn8YKQUy4OUf6SPMPvxo+FMw+VrsAPaDJ+zGUgdUesgTDEzcsw1/NBeb/ktxpQ5rCgdFU8Iwv4u8MCGjipUUABErjIGkCjw4Jy7S5rAVUGBEqbj/uU4M5rQFiYQQnCICwAlmJAgBaBdMS+BVKEMBpoFpy6zaUlnXxvhdDmCII/Os2fy0w6hIa2PjFC4A/N2cnVUgVSywPCYKnD7oWpNCFUH2mkmmfYDc0o6UDXEJJRBxHwnwy75yogHawaQRSEioL/Zow8VwLpLNMUIuGAGPjP5rmUb3fR4oCqKSo0OtJpIrSB6qYEd74p6lwK0lkg6Yt24NCndYCmISW55IhAkhMjAaiYMC7cIEhDUtO1OOpSmmJ9GKByuLiwcFWI8EuUIZMbLlLelPBJAsrmipSudFeygL/PFRfGlYTgPqAEd+5cceGUw6EXb1KA5rkiuRCkISEcpYjlt5HiwkEoQxc8FfDUhSELCcNCGtA2TVyYQBFCkE8VwZxp4sLZUoNka7UMWPI0kfKmMZI6trmDxIWD0oAE9YUGGurCUoCEhZYFNM8QKd0OK6nlWMquriUHqBwfUnrVEU8ZMnXi10DEz8xL8fvkcGHJ0HGEqTygZnJITig3idAZnez5gElwGBj/9moaApggAN/lf/72vwAAAP//E9IPLjBJAAA="); err != nil {
		panic("add binary content to resource manager failed: " + err.Error())
	}
}
