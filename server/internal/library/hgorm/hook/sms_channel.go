package hook

import (
	"context"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/utility/convert"
)

type SmsChannelSumma struct {
	Id          int64  `json:"id"                 description:"短信通道ID"`
	ChannelName string `json:"channelName"           description:"通道名称"`
	ChannelType string `json:"channelType"           description:"通道类型"`
}

// SmsChannelSummary 操作人摘要信息
var SmsChannelSummary = gdb.HookHandler{
	Select: func(ctx context.Context, in *gdb.HookSelectInput) (result gdb.Result, err error) {
		result, err = in.Next(ctx)
		if err != nil {
			return
		}

		var (
			channelIds []int64
		)

		for _, record := range result {
			if record["channel_id"].Int64() > 0 {
				channelIds = append(channelIds, record["channel_id"].Int64())
			}
		}

		channelIds = convert.UniqueSlice(channelIds)
		if len(channelIds) == 0 {
			return
		}

		var channels []*SmsChannelSumma
		if err = g.Model("sys_sms_channel").Ctx(ctx).WhereIn("id", channelIds).Scan(&channels); err != nil {
			return nil, err
		}

		if len(channels) == 0 {
			return
		}

		findChannel := func(id *gvar.Var) *SmsChannelSumma {
			for _, v := range channels {
				if v.Id == id.Int64() {
					return v
				}
			}
			return nil
		}

		for _, record := range result {
			if record["channel_id"].Int64() > 0 {
				record["smsChannelBySumma"] = gvar.New(findChannel(record["channel_id"]))
			}
		}
		return
	},
}
