// Package hook
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package hook

import (
	"context"
	"hotgo/internal/library/location"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// BusinessAreaLabel 业务区域标签
var BusinessAreaLabel = gdb.HookHandler{
	Select: func(ctx context.Context, in *gdb.HookSelectInput) (result gdb.Result, err error) {
		result, err = in.Next(ctx)
		if err != nil {
			return
		}

		parse := func(id int64, index int) {
			cityLabel, err := location.ParseSimpleRegion(ctx, id)
			if err != nil {
				g.Log().Warningf(ctx, "hook.BusinessAreaLabel parse err:%+v", err)
			}
			g.Log().Debugf(ctx, "BusinessAreaLabel parse cityLabel:%v", cityLabel)
			result[index]["area"] = gvar.New(cityLabel)
		}

		for i, record := range result {
			g.Log().Debugf(ctx, "BusinessAreaLabel record:%+v", record)

			// 优先使用驼峰命名的字段
			businessArea := record["businessArea"]
			if businessArea == nil || businessArea.IsEmpty() {
				businessArea = record["business_area"]
			}

			g.Log().Debugf(ctx, "BusinessAreaLabel businessArea:%+v", businessArea)
			if businessArea != nil && !businessArea.IsEmpty() {
				parse(businessArea.Int64(), i)
			}
		}
		return
	},
}
