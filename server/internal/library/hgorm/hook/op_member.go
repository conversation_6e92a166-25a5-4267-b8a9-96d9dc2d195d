// Package hook
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package hook

import (
	"context"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/utility/convert"
)

type OpMemberSumma struct {
	Id       int64  `json:"id"                 description:"管理员ID"`
	RealName string `json:"realName"           description:"真实姓名"`
	Username string `json:"username"           description:"帐号"`
	Avatar   string `json:"avatar"             description:"头像"`
}

// OpMemberSummary 操作人摘要信息
var OpMemberSummary = gdb.HookHandler{
	Select: func(ctx context.Context, in *gdb.HookSelectInput) (result gdb.Result, err error) {
		result, err = in.Next(ctx)
		if err != nil {
			return
		}

		var (
			opMemberIds []int64
		)

		for _, record := range result {
			if record["op_member_id"].Int64() > 0 {
				opMemberIds = append(opMemberIds, record["op_member_id"].Int64())
			}
		}

		opMemberIds = convert.UniqueSlice(opMemberIds)
		if len(opMemberIds) == 0 {
			return
		}

		var members []*OpMemberSumma
		if err = g.Model("admin_member").Ctx(ctx).WhereIn("id", opMemberIds).Scan(&members); err != nil {
			return nil, err
		}

		if len(members) == 0 {
			return
		}

		findMember := func(id *gvar.Var) *OpMemberSumma {
			for _, v := range members {
				if v.Id == id.Int64() {
					return v
				}
			}
			return nil
		}

		for _, record := range result {
			if record["op_member_id"].Int64() > 0 {
				record["opMemberBySumma"] = gvar.New(findMember(record["op_member_id"])) // TODO 存在findMember为nil的情况
			}
		}
		return
	},
}
