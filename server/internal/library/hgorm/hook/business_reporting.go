// Package hook
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package hook

import (
	"context"
	"hotgo/internal/library/location"
	"hotgo/utility/convert"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// BusinessReporting 商机报备信息处理
var BusinessReporting = gdb.HookHandler{
	Select: func(ctx context.Context, in *gdb.HookSelectInput) (result gdb.Result, err error) {
		g.Log().Debug(ctx, "BusinessReporting hook start")
		result, err = in.Next(ctx)
		if err != nil {
			g.Log().Error(ctx, "BusinessReporting hook error:", err)
			return
		}

		// 处理业务区域标签
		parseArea := func(id int64, index int) {
			g.Log().Debug(ctx, "BusinessReporting parsing area id:", id)
			cityLabel, err := location.ParseSimpleRegion(ctx, id)
			if err != nil {
				g.Log().Warningf(ctx, "hook.BusinessReporting parse area err:%+v", err)
			}
			g.Log().Debugf(ctx, "BusinessReporting parse cityLabel:%v", cityLabel)
			result[index]["area"] = gvar.New(cityLabel)
		}

		// 收集需要查询的用户ID
		var memberIds []int64
		for _, record := range result {
			// 处理业务区域
			businessArea := record["business_area"]
			g.Log().Debugf(ctx, "BusinessReporting businessArea value: %+v", businessArea)
			if businessArea != nil && !businessArea.IsEmpty() {
				parseArea(businessArea.Int64(), 0)
			}

			// 收集操作人ID
			if record["invoice_by"].Int64() > 0 {
				memberIds = append(memberIds, record["invoice_by"].Int64())
			}
			if record["payment_by"].Int64() > 0 {
				memberIds = append(memberIds, record["payment_by"].Int64())
			}
			if record["finish_by"].Int64() > 0 {
				memberIds = append(memberIds, record["finish_by"].Int64())
			}
			if record["delivery_by"].Int64() > 0 {
				memberIds = append(memberIds, record["delivery_by"].Int64())
			}
		}

		// 处理操作人信息
		memberIds = convert.UniqueSlice(memberIds)
		if len(memberIds) > 0 {
			var members []*MemberSumma
			if err = g.Model("admin_member").Ctx(ctx).WhereIn("id", memberIds).Scan(&members); err != nil {
				return nil, err
			}

			if len(members) > 0 {
				findMember := func(id *gvar.Var) *MemberSumma {
					for _, v := range members {
						if v.Id == id.Int64() {
							return v
						}
					}
					return nil
				}

				for _, record := range result {
					if record["invoice_by"].Int64() > 0 {
						if member := findMember(record["invoice_by"]); member != nil {
							record["invoiceByUsername"] = gvar.New(member.RealName)
						}
					}
					if record["payment_by"].Int64() > 0 {
						if member := findMember(record["payment_by"]); member != nil {
							record["paymentByUsername"] = gvar.New(member.RealName)
						}
					}
					if record["finish_by"].Int64() > 0 {
						if member := findMember(record["finish_by"]); member != nil {
							record["finishByUsername"] = gvar.New(member.RealName)
						}
					}
					if record["delivery_by"].Int64() > 0 {
						if member := findMember(record["delivery_by"]); member != nil {
							record["deliveryByUsername"] = gvar.New(member.RealName)
						}
					}
				}
			}
		}

		g.Log().Debug(ctx, "BusinessReporting hook end")
		return
	},
}
