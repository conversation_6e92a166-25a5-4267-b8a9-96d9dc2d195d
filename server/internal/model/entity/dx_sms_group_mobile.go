// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupMobile is the golang structure for table dx_sms_group_mobile.
type DxSmsGroupMobile struct {
	Id         int64       `json:"id"         orm:"id"          description:"ID"`
	MemberId   int64       `json:"memberId"   orm:"member_id"   description:"户用ID"`
	GroupId    int64       `json:"groupId"    orm:"group_id"    description:"群发ID"`
	Mobile     string      `json:"mobile"     orm:"mobile"      description:"手机号码"`
	TplVars    *gjson.Json `json:"tplVars"    orm:"tpl_vars"    description:"模板变量"`
	SendStatus int         `json:"sendStatus" orm:"send_status" description:"发送状态（1：未发送，2：已发送）"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:"创建时间"`
}
