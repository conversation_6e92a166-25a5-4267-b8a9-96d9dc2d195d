// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysNotifyConfig is the golang structure for table sys_notify_config.
type SysNotifyConfig struct {
	Id        int64       `json:"id"        orm:"id"         description:"主键ID"`
	Category  int         `json:"category"  orm:"category"   description:"通知分类"`
	Name      string      `json:"name"      orm:"name"       description:"通知名称"`
	SignText  string      `json:"signText"  orm:"sign_text"  description:"短信签名"`
	Content   string      `json:"content"   orm:"content"    description:"短信内容"`
	Mobiles   string      `json:"mobiles"   orm:"mobiles"    description:"通知号码"`
	MemberId  int64       `json:"memberId"  orm:"member_id"  description:"用户ID"`
	Status    int         `json:"status"    orm:"status"     description:"状态"`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`
}
