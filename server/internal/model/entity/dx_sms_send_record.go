// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendRecord is the golang structure for table dx_sms_send_record.
type DxSmsSendRecord struct {
	Id                int64       `json:"id"                orm:"id"                  description:"ID"`
	MemberId          int64       `json:"memberId"          orm:"member_id"           description:"用户ID"`
	GroupId           int64       `json:"groupId"           orm:"group_id"            description:"群发ID"`
	Mobile            string      `json:"mobile"            orm:"mobile"              description:"发送号码"`
	BatchNo           string      `json:"batchNo"           orm:"batch_no"            description:"群发批次号"`
	SmsType           int         `json:"smsType"           orm:"sms_type"            description:"短信类型（1：短信，2：闪信，3：数字短信）"`
	SignText          string      `json:"signText"          orm:"sign_text"           description:"短信签名"`
	ChannelId         int64       `json:"channelId"         orm:"channel_id"          description:"通道ID"`
	Content           string      `json:"content"           orm:"content"             description:"发送内容"`
	FeeNum            int         `json:"feeNum"            orm:"fee_num"             description:"计费条数"`
	UnitPrice         float64     `json:"unitPrice"         orm:"unit_price"          description:"通道原始单价"`
	Source            int         `json:"source"            orm:"source"              description:"发送来源（1：群发，2：接口）"`
	CreatedAt         *gtime.Time `json:"createdAt"         orm:"created_at"          description:"创建时间"`
	CreatedResult     int         `json:"createdResult"     orm:"created_result"      description:"创建结果（1：成功，2：失败）"`
	Ct                *gtime.Time `json:"ct"                orm:"ct"                  description:"提交时间"`
	Result            string      `json:"result"            orm:"result"              description:"提交结果（result=0 代表成功，result=其他代表提交失败）"`
	Sid               string      `json:"sid"               orm:"sid"                 description:"群发批次编号"`
	Seq               string      `json:"seq"               orm:"seq"                 description:"消息编号"`
	ReportStm         *gtime.Time `json:"reportStm"         orm:"report_stm"          description:"发送时间"`
	ReportSt          *gtime.Time `json:"reportSt"          orm:"report_st"           description:"状态时间"`
	ReportSc          string      `json:"reportSc"          orm:"report_sc"           description:"状态报告"`
	ReportChn         string      `json:"reportChn"         orm:"report_chn"          description:"中文状态标识"`
	FailedReason      string      `json:"failedReason"      orm:"failed_reason"       description:"提交失败原因"`
	ReportTimeoutFlag int         `json:"reportTimeoutFlag" orm:"report_timeout_flag" description:"状态报告超时标识（1：否，2：是）"`
}
