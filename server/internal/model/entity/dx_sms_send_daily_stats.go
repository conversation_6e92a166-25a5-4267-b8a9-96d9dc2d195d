// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendDailyStats is the golang structure for table dx_sms_send_daily_stats.
type DxSmsSendDailyStats struct {
	Id           int64       `json:"id"           orm:"id"            description:"ID"`
	MemberId     int64       `json:"memberId"     orm:"member_id"     description:"用户ID"`
	StatDate     *gtime.Time `json:"statDate"     orm:"stat_date"     description:"统计日期"`
	SmsType      int         `json:"smsType"      orm:"sms_type"      description:"短信类型"`
	TotalCount   int         `json:"totalCount"   orm:"total_count"   description:"发送量"`
	SuccessCount int         `json:"successCount" orm:"success_count" description:"成功量"`
	FeeCount     int         `json:"feeCount"     orm:"fee_count"     description:"计费条数"`
	FailCount    int         `json:"failCount"    orm:"fail_count"    description:"失败量"`
	UnknownCount int         `json:"unknownCount" orm:"unknown_count" description:"未知量"`
	SuccessRate  float64     `json:"successRate"  orm:"success_rate"  description:"成功率"`
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:"创建时间"`
}
