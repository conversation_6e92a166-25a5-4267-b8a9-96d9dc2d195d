// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxBusinessReporting is the golang structure for table dx_business_reporting.
type DxBusinessReporting struct {
	Id                  int64       `json:"id"                  orm:"id"                   description:"ID"`
	BusinessName        string      `json:"businessName"        orm:"business_name"        description:"业务名称"`
	BusinessArea        int64       `json:"businessArea"        orm:"business_area"        description:"业务区域"`
	CustomerManager     string      `json:"customerManager"     orm:"customer_manager"     description:"客户经理"`
	LinkTel             string      `json:"linkTel"             orm:"link_tel"             description:"联系电话"`
	CustomerOrg         string      `json:"customerOrg"         orm:"customer_org"         description:"客户单位"`
	BusinessRequirement *gjson.Json `json:"businessRequirement" orm:"business_requirement" description:"业务需求"`
	ServiceNumber       string      `json:"serviceNumber"       orm:"service_number"       description:"接入号"`
	Attachments         *gjson.Json `json:"attachments"         orm:"attachments"          description:"业务附件"`
	Remark              string      `json:"remark"              orm:"remark"               description:"备注"`
	BudgetAmount        float64     `json:"budgetAmount"        orm:"budget_amount"        description:"预算金额"`
	InvoiceAmount       float64     `json:"invoiceAmount"       orm:"invoice_amount"       description:"开票金额"`
	SettlementAmount    float64     `json:"settlementAmount"    orm:"settlement_amount"    description:"结算净额"`
	ExpenditureAmount   float64     `json:"expenditureAmount"   orm:"expenditure_amount"   description:"支出金额"`
	RetainedAmount      float64     `json:"retainedAmount"      orm:"retained_amount"      description:"留存金额"`
	RetentionRatio      float64     `json:"retentionRatio"      orm:"retention_ratio"      description:"留存比率"`
	CreatedTime         *gtime.Time `json:"createdTime"         orm:"created_time"         description:"创建时间"`
	Status              int         `json:"status"              orm:"status"               description:"状态（1：初始化，2：已开票，3：已回款，4：已完成，5：作废）"`
	InvoiceAttachments  *gjson.Json `json:"invoiceAttachments"  orm:"invoice_attachments"  description:"开票附件"`
	InvoiceDatetime     *gtime.Time `json:"invoiceDatetime"     orm:"invoice_datetime"     description:"开票时间"`
	InvoiceRemark       string      `json:"invoiceRemark"       orm:"invoice_remark"       description:"开票备注"`
	InvoiceBy           int64       `json:"invoiceBy"           orm:"invoice_by"           description:"开票操作人"`
	PaymentAttachments  *gjson.Json `json:"paymentAttachments"  orm:"payment_attachments"  description:"回款附件"`
	PaymentDatetime     *gtime.Time `json:"paymentDatetime"     orm:"payment_datetime"     description:"回款时间"`
	PaymentRemark       string      `json:"paymentRemark"       orm:"payment_remark"       description:"回款备注"`
	PaymentBy           int64       `json:"paymentBy"           orm:"payment_by"           description:"回款操作人"`
	FinishAttachments   *gjson.Json `json:"finishAttachments"   orm:"finish_attachments"   description:"完成附件"`
	FinishDatetime      *gtime.Time `json:"finishDatetime"      orm:"finish_datetime"      description:"完成时间"`
	FinishRemark        string      `json:"finishRemark"        orm:"finish_remark"        description:"完成备注"`
	FinishBy            int64       `json:"finishBy"            orm:"finish_by"            description:"完成操作人"`
	MemberId            int64       `json:"memberId"            orm:"member_id"            description:"关联用户ID"`
	DeliveryAmount      float64     `json:"deliveryAmount"      orm:"delivery_amount"      description:"交付金额"`
	DeliveryDatetime    *gtime.Time `json:"deliveryDatetime"    orm:"delivery_datetime"    description:"交付时间"`
	DeliveryAttachments *gjson.Json `json:"deliveryAttachments" orm:"delivery_attachments" description:"交付附件"`
	DeliveryRemark      string      `json:"deliveryRemark"      orm:"delivery_remark"      description:"交付备注"`
	DeliveryBy          int64       `json:"deliveryBy"          orm:"delivery_by"          description:"交付操作人"`
}
