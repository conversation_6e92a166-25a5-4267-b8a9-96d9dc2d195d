// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// AdminRoleCasbin is the golang structure for table admin_role_casbin.
type AdminRoleCasbin struct {
	Id    int64  `json:"id"    orm:"id"     description:""`
	PType string `json:"pType" orm:"p_type" description:""`
	V0    string `json:"v0"    orm:"v0"     description:""`
	V1    string `json:"v1"    orm:"v1"     description:""`
	V2    string `json:"v2"    orm:"v2"     description:""`
	V3    string `json:"v3"    orm:"v3"     description:""`
	V4    string `json:"v4"    orm:"v4"     description:""`
	V5    string `json:"v5"    orm:"v5"     description:""`
}
