// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Contact is the golang structure for table contact.
type Contact struct {
	Id         int64       `json:"id"         orm:"id"         description:"联系人ID"`
	MemberId   int64       `json:"memberId"   orm:"member_id"  description:"用户ID"`
	Name       string      `json:"name"       orm:"name"       description:"姓名"`
	Mobile     string      `json:"mobile"     orm:"mobile"     description:"手机号码"`
	Company    string      `json:"company"    orm:"company"    description:"公司名称"`
	Position   string      `json:"position"   orm:"position"   description:"职位"`
	Department string      `json:"department" orm:"department" description:"部门"`
	Email      string      `json:"email"      orm:"email"      description:"邮箱"`
	Remark     string      `json:"remark"     orm:"remark"     description:"备注"`
	Sort       int         `json:"sort"       orm:"sort"       description:"排序"`
	Status     int         `json:"status"     orm:"status"     description:"状态(1:正常 2:停用)"`
	CreatedBy  int64       `json:"createdBy"  orm:"created_by" description:"创建者"`
	UpdatedBy  int64       `json:"updatedBy"  orm:"updated_by" description:"更新者"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at" description:"创建时间"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at" description:"修改时间"`
	DeletedAt  *gtime.Time `json:"deletedAt"  orm:"deleted_at" description:"删除时间"`
}
