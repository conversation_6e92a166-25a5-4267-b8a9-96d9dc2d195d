// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupSend is the golang structure for table dx_sms_group_send.
type DxSmsGroupSend struct {
	Id             int64       `json:"id"             orm:"id"               description:"ID"`
	MemberId       int64       `json:"memberId"       orm:"member_id"        description:"用户ID"`
	BatchNo        string      `json:"batchNo"        orm:"batch_no"         description:"批次号"`
	SmsMode        int         `json:"smsMode"        orm:"sms_mode"         description:"短信模式（1：普通，2：模板）"`
	SendMode       int         `json:"sendMode"       orm:"send_mode"        description:"发送模式（1：立即发送，2：定时发送）"`
	SignId         int64       `json:"signId"         orm:"sign_id"          description:"签名ID"`
	ChannelId      int64       `json:"channelId"      orm:"channel_id"       description:"通道ID"`
	TemplateId     int64       `json:"templateId"     orm:"template_id"      description:"模板ID"`
	Content        string      `json:"content"        orm:"content"          description:"短信内容"`
	MobileNum      int         `json:"mobileNum"      orm:"mobile_num"       description:"号码数量"`
	Remark         string      `json:"remark"         orm:"remark"           description:"备注"`
	SendStatus     int         `json:"sendStatus"     orm:"send_status"      description:"发送状态（1：等待发送，2：正在发送，3：暂停发送，4：发送结束，5：发送失败）"`
	AuditStatus    int         `json:"auditStatus"    orm:"audit_status"     description:"审核状态（由企业配置决定）"`
	ScheduleTime   *gtime.Time `json:"scheduleTime"   orm:"schedule_time"    description:"定时发送时间"`
	CycleStartDate *gtime.Time `json:"cycleStartDate" orm:"cycle_start_date" description:"周期发送开始日期"`
	CycleEndDate   *gtime.Time `json:"cycleEndDate"   orm:"cycle_end_date"   description:"周期发送结束日期"`
	CycleStartTime *gtime.Time `json:"cycleStartTime" orm:"cycle_start_time" description:"周期发送开始时间"`
	CycleEndTime   *gtime.Time `json:"cycleEndTime"   orm:"cycle_end_time"   description:"周期发送结束时间"`
	CycleFreq      int         `json:"cycleFreq"      orm:"cycle_freq"       description:"周期发送频次/天"`
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"       description:"创建时间"`
	DeletedAt      *gtime.Time `json:"deletedAt"      orm:"deleted_at"       description:"删除时间"`
	AttachmentId   int64       `json:"attachmentId"   orm:"attachment_id"    description:"附件ID"`
	FileUrl        string      `json:"fileUrl"        orm:"file_url"         description:"文件路径"`
	Mobiles        string      `json:"mobiles"        orm:"mobiles"          description:"手机号码（手动输入）"`
	ContactGroups  *gjson.Json `json:"contactGroups"  orm:"contact_groups"   description:"通讯录分组"`
}
