// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsAuditLog is the golang structure for table dx_sms_audit_log.
type DxSmsAuditLog struct {
	Id          int64       `json:"id"          orm:"id"           description:"ID"`
	PkId        int64       `json:"pkId"        orm:"pk_id"        description:"资源ID"`
	AuditFlag   int         `json:"auditFlag"   orm:"audit_flag"   description:"审核标志（1：签名，2：模板，3：群发）"`
	AuditStatus int         `json:"auditStatus" orm:"audit_status" description:"审核状态"`
	AuditRemark string      `json:"auditRemark" orm:"audit_remark" description:"审核备注"`
	CreatedBy   int64       `json:"createdBy"   orm:"created_by"   description:"创建者"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`
}
