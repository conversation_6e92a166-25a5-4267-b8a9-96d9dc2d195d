// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupTemplate is the golang structure for table dx_sms_group_template.
type DxSmsGroupTemplate struct {
	Id          int64       `json:"id"          orm:"id"           description:"ID"`
	MemberId    int64       `json:"memberId"    orm:"member_id"    description:"用户ID"`
	TplName     string      `json:"tplName"     orm:"tpl_name"     description:"模板名称"`
	VarNum      int         `json:"varNum"      orm:"var_num"      description:"变量个数"`
	TplContent  string      `json:"tplContent"  orm:"tpl_content"  description:"模板内容"`
	Status      int         `json:"status"      orm:"status"       description:"状态"`
	AuditStatus int         `json:"auditStatus" orm:"audit_status" description:"审核状态"`
	Remark      string      `json:"remark"      orm:"remark"       description:"备注"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"   description:"删除时间"`
}
