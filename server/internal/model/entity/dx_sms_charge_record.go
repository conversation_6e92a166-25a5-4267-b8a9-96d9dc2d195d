// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsChargeRecord is the golang structure for table dx_sms_charge_record.
type DxSmsChargeRecord struct {
	Id         int64       `json:"id"         orm:"id"           description:"ID"`
	MemberId   int64       `json:"memberId"   orm:"member_id"    description:"用户ID"`
	OpMemberId int64       `json:"opMemberId" orm:"op_member_id" description:"操作人ID"`
	OpMode     int         `json:"opMode"     orm:"op_mode"      description:"操作类型"`
	Num        int         `json:"num"        orm:"num"          description:"操作条数"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"   description:"操作时间"`
}
