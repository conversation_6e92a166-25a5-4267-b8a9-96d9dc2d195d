// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsMoRecord is the golang structure for table dx_sms_mo_record.
type DxSmsMoRecord struct {
	Id        int64       `json:"id"        orm:"id"         description:"ID"`
	MemberId  int64       `json:"memberId"  orm:"member_id"  description:"用户ID"`
	Ct        *gtime.Time `json:"ct"        orm:"ct"         description:"上行时间"`
	Mobile    string      `json:"mobile"    orm:"mobile"     description:"产生上行的手机号码"`
	Sp        string      `json:"sp"        orm:"sp"         description:"上行号码所属运营商"`
	Lc        string      `json:"lc"        orm:"lc"         description:"上行号码所属省市"`
	Ext       string      `json:"ext"       orm:"ext"        description:"接入码"`
	Msg       string      `json:"msg"       orm:"msg"        description:"目标号码上行内容"`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
	ChannelId int64       `json:"channelId" orm:"channel_id" description:"通道ID"`
	Uid       int         `json:"uid"       orm:"uid"        description:"用户账号所属编号，唯一"`
	Uname     string      `json:"uname"     orm:"uname"      description:"用户账号"`
	IsFetch   int         `json:"isFetch"   orm:"is_fetch"   description:"是否已通过api接口获取"`
	FetchTime *gtime.Time `json:"fetchTime" orm:"fetch_time" description:"获取时间"`
}
