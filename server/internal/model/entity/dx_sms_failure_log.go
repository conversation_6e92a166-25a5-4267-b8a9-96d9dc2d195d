// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsFailureLog is the golang structure for table dx_sms_failure_log.
type DxSmsFailureLog struct {
	Id               int64       `json:"id"               orm:"id"                 description:"主键ID"`
	OriginalRecordId int64       `json:"originalRecordId" orm:"original_record_id" description:"原始发送记录ID"`
	MemberId         int64       `json:"memberId"         orm:"member_id"          description:"会员ID"`
	BatchNo          string      `json:"batchNo"          orm:"batch_no"           description:"批次号"`
	Mobile           string      `json:"mobile"           orm:"mobile"             description:"手机号"`
	ChannelId        int64       `json:"channelId"        orm:"channel_id"         description:"失败的通道ID"`
	FailedReason     string      `json:"failedReason"     orm:"failed_reason"      description:"失败原因(状态码)"`
	FailedTime       *gtime.Time `json:"failedTime"       orm:"failed_time"        description:"失败时间(状态报告时间)"`
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"         description:"记录创建时间"`
}
