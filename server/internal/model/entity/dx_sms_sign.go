// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSign is the golang structure for table dx_sms_sign.
type DxSmsSign struct {
	Id          int64       `json:"id"          orm:"id"           description:"ID"`
	MemberId    int64       `json:"memberId"    orm:"member_id"    description:"用户ID"`
	SignText    string      `json:"signText"    orm:"sign_text"    description:"签名文本"`
	Remark      string      `json:"remark"      orm:"remark"       description:"备注"`
	Status      int         `json:"status"      orm:"status"       description:"状态"`
	AuditStatus int         `json:"auditStatus" orm:"audit_status" description:"审核状态"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:"更新时间"`
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"   description:"删除时间"`
}
