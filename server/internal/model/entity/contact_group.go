// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ContactGroup is the golang structure for table contact_group.
type ContactGroup struct {
	Id          int64       `json:"id"          orm:"id"          description:"分组ID"`
	MemberId    int64       `json:"memberId"    orm:"member_id"   description:"用户ID"`
	Pid         int64       `json:"pid"         orm:"pid"         description:"上级分组ID"`
	Name        string      `json:"name"        orm:"name"        description:"分组名称"`
	Description string      `json:"description" orm:"description" description:"分组描述"`
	Sort        int         `json:"sort"        orm:"sort"        description:"排序"`
	Status      int         `json:"status"      orm:"status"      description:"状态(1:正常 2:停用)"`
	CreatedBy   int64       `json:"createdBy"   orm:"created_by"  description:"创建者"`
	UpdatedBy   int64       `json:"updatedBy"   orm:"updated_by"  description:"更新者"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"修改时间"`
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  description:"删除时间"`
}
