// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendTmp is the golang structure for table dx_sms_send_tmp.
type DxSmsSendTmp struct {
	Id               int64       `json:"id"               orm:"id"                 description:"ID"`
	OriginalRecordId int64       `json:"originalRecordId" orm:"original_record_id" description:"原始发送记录ID(用于标识重发短信)"`
	MemberId         int64       `json:"memberId"         orm:"member_id"          description:"用户ID"`
	GroupId          int64       `json:"groupId"          orm:"group_id"           description:"群发ID"`
	BatchNo          string      `json:"batchNo"          orm:"batch_no"           description:"批次号"`
	Mobile           string      `json:"mobile"           orm:"mobile"             description:"手机号码"`
	SmsType          int         `json:"smsType"          orm:"sms_type"           description:"短信类型（1：短信）"`
	ChannelId        int64       `json:"channelId"        orm:"channel_id"         description:"通道ID"`
	UnitPrice        float64     `json:"unitPrice"        orm:"unit_price"         description:"通道单价"`
	FeeNum           int         `json:"feeNum"           orm:"fee_num"            description:"计费条数"`
	SignText         string      `json:"signText"         orm:"sign_text"          description:"短信签名"`
	Content          string      `json:"content"          orm:"content"            description:"短信内容"`
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"         description:"创建时间"`
	Source           int         `json:"source"           orm:"source"             description:"发送来源"`
}
