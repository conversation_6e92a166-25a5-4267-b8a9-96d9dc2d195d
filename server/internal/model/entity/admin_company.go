// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
)

// AdminCompany is the golang structure for table admin_company.
type AdminCompany struct {
	Id                 int64       `json:"id"                 orm:"id"                    description:"ID"`
	Pid                int64       `json:"pid"                orm:"pid"                   description:"父ID"`
	DeptId             int64       `json:"deptId"             orm:"dept_id"               description:"部门ID"`
	MemberId           int64       `json:"memberId"           orm:"member_id"             description:"用户ID"`
	CompanyName        string      `json:"companyName"        orm:"company_name"          description:"名称"`
	CompanyAccount     string      `json:"companyAccount"     orm:"company_account"       description:"账号"`
	Balance            float64     `json:"balance"            orm:"balance"               description:"余额"`
	SmsBalance         int         `json:"smsBalance"         orm:"sms_balance"           description:"短信余额"`
	SmsFrozen          int         `json:"smsFrozen"          orm:"sms_frozen"            description:"短信冻结"`
	Leader             string      `json:"leader"             orm:"leader"                description:"负责人"`
	Phone              string      `json:"phone"              orm:"phone"                 description:"联系电话"`
	Email              string      `json:"email"              orm:"email"                 description:"邮箱"`
	Address            string      `json:"address"            orm:"address"               description:"地址"`
	Attachfiles        *gjson.Json `json:"attachfiles"        orm:"attachfiles"           description:"附件列表"`
	ChannelId          int64       `json:"channelId"          orm:"channel_id"            description:"短信通道id"`
	SmsType            int         `json:"smsType"            orm:"sms_type"              description:"短信类型（1：短信，2：闪信，3：数字短信）"`
	GroupSendAuditMode int         `json:"groupSendAuditMode" orm:"group_send_audit_mode" description:"群发鉴权模式（1：鉴权，2：放行）"`
	TemplateAuditMode  int         `json:"templateAuditMode"  orm:"template_audit_mode"   description:"模板鉴权模式（1：鉴权，2：放行）"`
	SecretKey          string      `json:"secretKey"          orm:"secret_key"            description:""`
	Level              int         `json:"level"              orm:"level"                 description:""`
	Tree               string      `json:"tree"               orm:"tree"                  description:""`
	Sort               int         `json:"sort"               orm:"sort"                  description:""`
	SmsAmount          int         `json:"smsAmount"          orm:"sms_amount"            description:"短信总额"`
}
