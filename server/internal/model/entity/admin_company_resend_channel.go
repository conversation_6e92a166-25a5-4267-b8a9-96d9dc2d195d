// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminCompanyResendChannel is the golang structure for table admin_company_resend_channel.
type AdminCompanyResendChannel struct {
	Id        int64       `json:"id"        orm:"id"         description:"主键ID"`
	MemberId  int64       `json:"memberId"  orm:"member_id"  description:"企业用户ID"`
	ChannelId int64       `json:"channelId" orm:"channel_id" description:"通道ID"`
	Priority  int         `json:"priority"  orm:"priority"   description:"优先级(数值越小优先级越高)"`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`
} 