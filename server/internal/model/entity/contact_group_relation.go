// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ContactGroupRelation is the golang structure for table contact_group_relation.
type ContactGroupRelation struct {
	Id        int64       `json:"id"        orm:"id"         description:"关系ID"`
	ContactId int64       `json:"contactId" orm:"contact_id" description:"联系人ID"`
	GroupId   int64       `json:"groupId"   orm:"group_id"   description:"分组ID"`
	MemberId  int64       `json:"memberId"  orm:"member_id"  description:"用户ID（冗余字段，便于查询）"`
	CreatedBy int64       `json:"createdBy" orm:"created_by" description:"创建者"`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`
}
