// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysSmsChannel is the golang structure for table sys_sms_channel.
type SysSmsChannel struct {
	Id          int64       `json:"id"          orm:"id"           description:"ID"`
	ChannelName string      `json:"channelName" orm:"channel_name" description:"通道名称"`
	Protocol    int         `json:"protocol"    orm:"protocol"     description:"通道协议（0：http，1：cmpp，2：smpp，3：SGIP）"`
	Flag        int         `json:"flag"        orm:"flag"         description:"运营商标识（0：三网，1：移动，2：电信，3：联通）"`
	UnitPrice   float64     `json:"unitPrice"   orm:"unit_price"   description:"短信单价"`
	Host        string      `json:"host"        orm:"host"         description:"接口地址"`
	Account     string      `json:"account"     orm:"account"      description:"接口账号"`
	Passwd      string      `json:"passwd"      orm:"passwd"       description:"接口密码"`
	Params      string      `json:"params"      orm:"params"       description:"接口参数"`
	ExtCode     string      `json:"extCode"     orm:"ext_code"     description:"扩展码"`
	Description string      `json:"description" orm:"description"  description:"描述"`
	Pid         int64       `json:"pid"         orm:"pid"          description:"上级ID"`
	Level       int         `json:"level"       orm:"level"        description:"关系树级别"`
	Tree        string      `json:"tree"        orm:"tree"         description:"关系树"`
	Sort        int         `json:"sort"        orm:"sort"         description:"排序"`
	Status      int         `json:"status"      orm:"status"       description:"状态"`
	CreatedBy   int64       `json:"createdBy"   orm:"created_by"   description:"创建者"`
	UpdatedBy   int64       `json:"updatedBy"   orm:"updated_by"   description:"更新者"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:"修改时间"`
	ChannelType string      `json:"channelType" orm:"channel_type" description:"通道类型"`
}
