// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSign is the golang structure of table hg_dx_sms_sign for DAO operations like Where/Data.
type DxSmsSign struct {
	g.Meta      `orm:"table:hg_dx_sms_sign, do:true"`
	Id          interface{} // ID
	MemberId    interface{} // 用户ID
	SignText    interface{} // 签名文本
	Remark      interface{} // 备注
	Status      interface{} // 状态
	AuditStatus interface{} // 审核状态
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
	DeletedAt   *gtime.Time // 删除时间
}
