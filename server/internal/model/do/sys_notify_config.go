// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysNotifyConfig is the golang structure of table hg_sys_notify_config for DAO operations like Where/Data.
type SysNotifyConfig struct {
	g.Meta    `orm:"table:hg_sys_notify_config, do:true"`
	Id        interface{} // 主键ID
	Category  interface{} // 通知分类
	Name      interface{} // 通知名称
	SignText  interface{} // 短信签名
	Content   interface{} // 短信内容
	Mobiles   interface{} // 通知号码
	MemberId  interface{} // 用户ID
	Status    interface{} // 状态
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
