// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
)

// AdminCompany is the golang structure of table hg_admin_company for DAO operations like Where/Data.
type AdminCompany struct {
	g.Meta             `orm:"table:hg_admin_company, do:true"`
	Id                 interface{} // ID
	Pid                interface{} // 父ID
	DeptId             interface{} // 部门ID
	MemberId           interface{} // 用户ID
	CompanyName        interface{} // 名称
	CompanyAccount     interface{} // 账号
	Balance            interface{} // 余额
	SmsBalance         interface{} // 短信余额
	SmsFrozen          interface{} // 短信冻结
	Leader             interface{} // 负责人
	Phone              interface{} // 联系电话
	Email              interface{} // 邮箱
	Address            interface{} // 地址
	Attachfiles        *gjson.Json // 附件列表
	ChannelId          interface{} // 短信通道id
	SmsType            interface{} // 短信类型（1：短信，2：闪信，3：数字短信）
	GroupSendAuditMode interface{} // 群发鉴权模式（1：鉴权，2：放行）
	TemplateAuditMode  interface{} // 模板鉴权模式（1：鉴权，2：放行）
	SecretKey          interface{} //
	Level              interface{} //
	Tree               interface{} //
	Sort               interface{} //
	SmsAmount          interface{} // 短信总额
}
