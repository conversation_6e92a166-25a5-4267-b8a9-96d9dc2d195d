// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Contact is the golang structure of table hg_contact for DAO operations like Where/Data.
type Contact struct {
	g.Meta     `orm:"table:hg_contact, do:true"`
	Id         interface{} // 联系人ID
	MemberId   interface{} // 用户ID
	Name       interface{} // 姓名
	Mobile     interface{} // 手机号码
	Company    interface{} // 公司名称
	Position   interface{} // 职位
	Department interface{} // 部门
	Email      interface{} // 邮箱
	Remark     interface{} // 备注
	Sort       interface{} // 排序
	Status     interface{} // 状态(1:正常 2:停用)
	CreatedBy  interface{} // 创建者
	UpdatedBy  interface{} // 更新者
	CreatedAt  *gtime.Time // 创建时间
	UpdatedAt  *gtime.Time // 修改时间
	DeletedAt  *gtime.Time // 删除时间
}
