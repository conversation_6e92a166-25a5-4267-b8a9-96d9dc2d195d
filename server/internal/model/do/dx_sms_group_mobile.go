// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupMobile is the golang structure of table hg_dx_sms_group_mobile for DAO operations like Where/Data.
type DxSmsGroupMobile struct {
	g.Meta     `orm:"table:hg_dx_sms_group_mobile, do:true"`
	Id         interface{} // ID
	MemberId   interface{} // 户用ID
	GroupId    interface{} // 群发ID
	Mobile     interface{} // 手机号码
	TplVars    *gjson.Json // 模板变量
	SendStatus interface{} // 发送状态（1：未发送，2：已发送）
	CreatedAt  *gtime.Time // 创建时间
}
