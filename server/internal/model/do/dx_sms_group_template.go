// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupTemplate is the golang structure of table hg_dx_sms_group_template for DAO operations like Where/Data.
type DxSmsGroupTemplate struct {
	g.Meta      `orm:"table:hg_dx_sms_group_template, do:true"`
	Id          interface{} // ID
	MemberId    interface{} // 用户ID
	TplName     interface{} // 模板名称
	VarNum      interface{} // 变量个数
	TplContent  interface{} // 模板内容
	Status      interface{} // 状态
	AuditStatus interface{} // 审核状态
	Remark      interface{} // 备注
	CreatedAt   *gtime.Time // 创建时间
	DeletedAt   *gtime.Time // 删除时间
}
