// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysSmsChannel is the golang structure of table hg_sys_sms_channel for DAO operations like Where/Data.
type SysSmsChannel struct {
	g.Meta      `orm:"table:hg_sys_sms_channel, do:true"`
	Id          interface{} // ID
	ChannelName interface{} // 通道名称
	Protocol    interface{} // 通道协议（0：http，1：cmpp，2：smpp，3：SGIP）
	Flag        interface{} // 运营商标识（0：三网，1：移动，2：电信，3：联通）
	UnitPrice   interface{} // 短信单价
	Host        interface{} // 接口地址
	Account     interface{} // 接口账号
	Passwd      interface{} // 接口密码
	Params      interface{} // 接口参数
	ExtCode     interface{} // 扩展码
	Description interface{} // 描述
	Pid         interface{} // 上级ID
	Level       interface{} // 关系树级别
	Tree        interface{} // 关系树
	Sort        interface{} // 排序
	Status      interface{} // 状态
	CreatedBy   interface{} // 创建者
	UpdatedBy   interface{} // 更新者
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 修改时间
	ChannelType interface{} // 通道类型
}
