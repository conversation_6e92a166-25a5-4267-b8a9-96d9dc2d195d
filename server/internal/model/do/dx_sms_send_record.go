// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendRecord is the golang structure of table hg_dx_sms_send_record for DAO operations like Where/Data.
type DxSmsSendRecord struct {
	g.Meta            `orm:"table:hg_dx_sms_send_record, do:true"`
	Id                interface{} // ID
	MemberId          interface{} // 用户ID
	GroupId           interface{} // 群发ID
	Mobile            interface{} // 发送号码
	BatchNo           interface{} // 群发批次号
	SmsType           interface{} // 短信类型（1：短信，2：闪信，3：数字短信）
	SignText          interface{} // 短信签名
	ChannelId         interface{} // 通道ID
	Content           interface{} // 发送内容
	FeeNum            interface{} // 计费条数
	UnitPrice         interface{} // 通道原始单价
	Source            interface{} // 发送来源（1：群发，2：接口）
	CreatedAt         *gtime.Time // 创建时间
	CreatedResult     interface{} // 创建结果（1：成功，2：失败）
	Ct                *gtime.Time // 提交时间
	Result            interface{} // 提交结果（result=0 代表成功，result=其他代表提交失败）
	Sid               interface{} // 群发批次编号
	Seq               interface{} // 消息编号
	ReportStm         *gtime.Time // 发送时间
	ReportSt          *gtime.Time // 状态时间
	ReportSc          interface{} // 状态报告
	ReportChn         interface{} // 中文状态标识
	FailedReason      interface{} // 提交失败原因
	ReportTimeoutFlag interface{} // 状态报告超时标识（1：否，2：是）
}
