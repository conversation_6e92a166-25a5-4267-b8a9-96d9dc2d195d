// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminCompanyResendChannel is the golang structure of table hg_admin_company_resend_channel for DAO operations like Where/Data.
type AdminCompanyResendChannel struct {
	g.Meta    `orm:"table:hg_admin_company_resend_channel, do:true"`
	Id        interface{} // 主键ID
	MemberId  interface{} // 企业用户ID
	ChannelId interface{} // 通道ID
	Priority  interface{} // 优先级(数值越小优先级越高)
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
