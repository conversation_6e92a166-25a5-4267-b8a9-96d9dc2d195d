// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendTmp is the golang structure of table hg_dx_sms_send_tmp for DAO operations like Where/Data.
type DxSmsSendTmp struct {
	g.Meta           `orm:"table:hg_dx_sms_send_tmp, do:true"`
	Id               interface{} // ID
	OriginalRecordId interface{} // 原始发送记录ID(用于标识重发短信)
	MemberId         interface{} // 用户ID
	GroupId          interface{} // 群发ID
	BatchNo          interface{} // 批次号
	Mobile           interface{} // 手机号码
	SmsType          interface{} // 短信类型（1：短信）
	ChannelId        interface{} // 通道ID
	UnitPrice        interface{} // 通道单价
	FeeNum           interface{} // 计费条数
	SignText         interface{} // 短信签名
	Content          interface{} // 短信内容
	CreatedAt        *gtime.Time // 创建时间
	Source           interface{} // 发送来源
}
