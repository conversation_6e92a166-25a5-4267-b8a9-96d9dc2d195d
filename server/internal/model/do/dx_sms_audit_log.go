// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsAuditLog is the golang structure of table hg_dx_sms_audit_log for DAO operations like Where/Data.
type DxSmsAuditLog struct {
	g.Meta      `orm:"table:hg_dx_sms_audit_log, do:true"`
	Id          interface{} // ID
	PkId        interface{} // 资源ID
	AuditFlag   interface{} // 审核标志（1：签名，2：模板，3：群发）
	AuditStatus interface{} // 审核状态
	AuditRemark interface{} // 审核备注
	CreatedBy   interface{} // 创建者
	CreatedAt   *gtime.Time // 创建时间
}
