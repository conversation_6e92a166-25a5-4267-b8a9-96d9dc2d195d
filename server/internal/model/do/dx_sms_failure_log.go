// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsFailureLog is the golang structure of table hg_dx_sms_failure_log for DAO operations like Where/Data.
type DxSmsFailureLog struct {
	g.Meta           `orm:"table:hg_dx_sms_failure_log, do:true"`
	Id               interface{} // 主键ID
	OriginalRecordId interface{} // 原始发送记录ID
	MemberId         interface{} // 会员ID
	BatchNo          interface{} // 批次号
	Mobile           interface{} // 手机号
	ChannelId        interface{} // 失败的通道ID
	FailedReason     interface{} // 失败原因(状态码)
	FailedTime       *gtime.Time // 失败时间(状态报告时间)
	CreatedAt        *gtime.Time // 记录创建时间
}
