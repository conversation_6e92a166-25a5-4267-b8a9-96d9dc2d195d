// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxBusinessReporting is the golang structure of table hg_dx_business_reporting for DAO operations like Where/Data.
type DxBusinessReporting struct {
	g.Meta              `orm:"table:hg_dx_business_reporting, do:true"`
	Id                  interface{} // ID
	BusinessName        interface{} // 业务名称
	BusinessArea        interface{} // 业务区域
	CustomerManager     interface{} // 客户经理
	LinkTel             interface{} // 联系电话
	CustomerOrg         interface{} // 客户单位
	BusinessRequirement *gjson.Json // 业务需求
	ServiceNumber       interface{} // 接入号
	Attachments         *gjson.Json // 业务附件
	Remark              interface{} // 备注
	BudgetAmount        interface{} // 预算金额
	InvoiceAmount       interface{} // 开票金额
	SettlementAmount    interface{} // 结算净额
	ExpenditureAmount   interface{} // 支出金额
	RetainedAmount      interface{} // 留存金额
	RetentionRatio      interface{} // 留存比率
	CreatedTime         *gtime.Time // 创建时间
	Status              interface{} // 状态（1：初始化，2：已开票，3：已回款，4：已完成，5：作废）
	InvoiceAttachments  *gjson.Json // 开票附件
	InvoiceDatetime     *gtime.Time // 开票时间
	InvoiceRemark       interface{} // 开票备注
	InvoiceBy           interface{} // 开票操作人
	PaymentAttachments  *gjson.Json // 回款附件
	PaymentDatetime     *gtime.Time // 回款时间
	PaymentRemark       interface{} // 回款备注
	PaymentBy           interface{} // 回款操作人
	FinishAttachments   *gjson.Json // 完成附件
	FinishDatetime      *gtime.Time // 完成时间
	FinishRemark        interface{} // 完成备注
	FinishBy            interface{} // 完成操作人
	MemberId            interface{} // 关联用户ID
	DeliveryAmount      interface{} // 交付金额
	DeliveryDatetime    *gtime.Time // 交付时间
	DeliveryAttachments *gjson.Json // 交付附件
	DeliveryRemark      interface{} // 交付备注
	DeliveryBy          interface{} // 交付操作人
}
