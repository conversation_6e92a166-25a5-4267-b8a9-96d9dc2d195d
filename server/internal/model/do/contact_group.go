// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ContactGroup is the golang structure of table hg_contact_group for DAO operations like Where/Data.
type ContactGroup struct {
	g.Meta      `orm:"table:hg_contact_group, do:true"`
	Id          interface{} // 分组ID
	MemberId    interface{} // 用户ID
	Pid         interface{} // 上级分组ID
	Name        interface{} // 分组名称
	Description interface{} // 分组描述
	Sort        interface{} // 排序
	Status      interface{} // 状态(1:正常 2:停用)
	CreatedBy   interface{} // 创建者
	UpdatedBy   interface{} // 更新者
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 修改时间
	DeletedAt   *gtime.Time // 删除时间
}
