// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendDailyStats is the golang structure of table hg_dx_sms_send_daily_stats for DAO operations like Where/Data.
type DxSmsSendDailyStats struct {
	g.Meta       `orm:"table:hg_dx_sms_send_daily_stats, do:true"`
	Id           interface{} // ID
	MemberId     interface{} // 用户ID
	StatDate     *gtime.Time // 统计日期
	SmsType      interface{} // 短信类型
	TotalCount   interface{} // 发送量
	SuccessCount interface{} // 成功量
	FeeCount     interface{} // 计费条数
	FailCount    interface{} // 失败量
	UnknownCount interface{} // 未知量
	SuccessRate  interface{} // 成功率
	CreatedAt    *gtime.Time // 创建时间
}
