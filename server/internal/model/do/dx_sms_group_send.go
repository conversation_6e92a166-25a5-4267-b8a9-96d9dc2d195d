// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupSend is the golang structure of table hg_dx_sms_group_send for DAO operations like Where/Data.
type DxSmsGroupSend struct {
	g.Meta         `orm:"table:hg_dx_sms_group_send, do:true"`
	Id             interface{} // ID
	MemberId       interface{} // 用户ID
	BatchNo        interface{} // 批次号
	SmsMode        interface{} // 短信模式（1：普通，2：模板）
	SendMode       interface{} // 发送模式（1：立即发送，2：定时发送）
	SignId         interface{} // 签名ID
	ChannelId      interface{} // 通道ID
	TemplateId     interface{} // 模板ID
	Content        interface{} // 短信内容
	MobileNum      interface{} // 号码数量
	Remark         interface{} // 备注
	SendStatus     interface{} // 发送状态（1：等待发送，2：正在发送，3：暂停发送，4：发送结束，5：发送失败）
	AuditStatus    interface{} // 审核状态（由企业配置决定）
	ScheduleTime   *gtime.Time // 定时发送时间
	CycleStartDate *gtime.Time // 周期发送开始日期
	CycleEndDate   *gtime.Time // 周期发送结束日期
	CycleStartTime *gtime.Time // 周期发送开始时间
	CycleEndTime   *gtime.Time // 周期发送结束时间
	CycleFreq      interface{} // 周期发送频次/天
	CreatedAt      *gtime.Time // 创建时间
	DeletedAt      *gtime.Time // 删除时间
	AttachmentId   interface{} // 附件ID
	FileUrl        interface{} // 文件路径
	Mobiles        interface{} // 手机号码（手动输入）
	ContactGroups  *gjson.Json // 通讯录分组
}
