// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ContactGroupRelation is the golang structure of table hg_contact_group_relation for DAO operations like Where/Data.
type ContactGroupRelation struct {
	g.Meta    `orm:"table:hg_contact_group_relation, do:true"`
	Id        interface{} // 关系ID
	ContactId interface{} // 联系人ID
	GroupId   interface{} // 分组ID
	MemberId  interface{} // 用户ID（冗余字段，便于查询）
	CreatedBy interface{} // 创建者
	CreatedAt *gtime.Time // 创建时间
}
