// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsChargeRecord is the golang structure of table hg_dx_sms_charge_record for DAO operations like Where/Data.
type DxSmsChargeRecord struct {
	g.Meta     `orm:"table:hg_dx_sms_charge_record, do:true"`
	Id         interface{} // ID
	MemberId   interface{} // 用户ID
	OpMemberId interface{} // 操作人ID
	OpMode     interface{} // 操作类型
	Num        interface{} // 操作条数
	CreatedAt  *gtime.Time // 操作时间
}
