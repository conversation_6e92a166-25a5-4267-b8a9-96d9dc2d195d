// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsMoRecord is the golang structure of table hg_dx_sms_mo_record for DAO operations like Where/Data.
type DxSmsMoRecord struct {
	g.Meta    `orm:"table:hg_dx_sms_mo_record, do:true"`
	Id        interface{} // ID
	MemberId  interface{} // 用户ID
	Ct        *gtime.Time // 上行时间
	Mobile    interface{} // 产生上行的手机号码
	Sp        interface{} // 上行号码所属运营商
	Lc        interface{} // 上行号码所属省市
	Ext       interface{} // 接入码
	Msg       interface{} // 目标号码上行内容
	CreatedAt *gtime.Time // 创建时间
	ChannelId interface{} // 通道ID
	Uid       interface{} // 用户账号所属编号，唯一
	Uname     interface{} // 用户账号
	IsFetch   interface{} // 是否已通过api接口获取
	FetchTime *gtime.Time // 获取时间
}
