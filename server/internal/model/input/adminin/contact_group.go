// Package adminin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package adminin

import (
	"context"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ContactGroupUpdateFieldsInp 修改通讯录分组字段过滤
type ContactGroupUpdateFieldsInp struct {
	Pid         int64  `json:"pid"         description:"父分组ID"`
	Name        string `json:"name"        description:"分组名称"`
	Description string `json:"description" description:"分组描述"`
	Sort        int    `json:"sort"        description:"排序"`
	Status      int    `json:"status"      description:"状态(1:正常 2:停用)"`
	UpdatedBy   int64  `json:"updatedBy"   description:"更新者"`
}

// ContactGroupInsertFieldsInp 新增通讯录分组字段过滤
type ContactGroupInsertFieldsInp struct {
	Pid         int64  `json:"pid"         description:"父分组ID"`
	MemberId    int64  `json:"memberId"    description:"用户ID"`
	Name        string `json:"name"        description:"分组名称"`
	Description string `json:"description" description:"分组描述"`
	Sort        int    `json:"sort"        description:"排序"`
	Status      int    `json:"status"      description:"状态(1:正常 2:停用)"`
	CreatedBy   int64  `json:"createdBy"   description:"创建者"`
	UpdatedBy   int64  `json:"updatedBy"   description:"更新者"`
}

// ContactGroupEditInp 修改/新增通讯录分组
type ContactGroupEditInp struct {
	entity.ContactGroup
}

func (in *ContactGroupEditInp) Filter(ctx context.Context) (err error) {
	// 验证分组名称
	if err := g.Validator().Rules("required").Data(in.Name).Messages("分组名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type ContactGroupEditModel struct{}

// ContactGroupDeleteInp 删除通讯录分组
type ContactGroupDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *ContactGroupDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactGroupDeleteModel struct{}

// ContactGroupViewInp 获取指定通讯录分组信息
type ContactGroupViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *ContactGroupViewInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactGroupViewModel struct {
	entity.ContactGroup
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
}

// ContactGroupListInp 获取通讯录分组列表
type ContactGroupListInp struct {
	form.PageReq
	form.StatusReq
	Id          int64   `json:"id"          dc:"分组ID"`
	Pid         int64   `json:"pid"         dc:"父分组ID"`
	Name        string  `json:"name"        dc:"分组名称"`
	ParentName  string  `json:"parentName"  dc:"父分组名称"`
	Description string  `json:"description" dc:"分组描述"`
	CreatedAt   []int64 `json:"createdAt"   dc:"创建时间"`
}

func (in *ContactGroupListInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactGroupListModel struct {
	Id          int64       `json:"id"          dc:"分组ID"`
	Pid         int64       `json:"pid"         dc:"父分组ID"`
	Name        string      `json:"name"        dc:"分组名称"`
	ParentName  string      `json:"parentName"  dc:"父分组名称"`
	Description string      `json:"description" dc:"分组描述"`
	Sort        int         `json:"sort"        dc:"排序"`
	Status      int         `json:"status"      dc:"状态(1:正常 2:停用)"`
	CreatedAt   *gtime.Time `json:"createdAt"   dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   dc:"修改时间"`
}

// ContactGroupExportModel 导出通讯录分组
type ContactGroupExportModel struct {
	Id          int64       `json:"id"          dc:"分组ID"`
	Pid         int64       `json:"pid"         dc:"父分组ID"`
	Name        string      `json:"name"        dc:"分组名称"`
	Description string      `json:"description" dc:"分组描述"`
	Sort        int         `json:"sort"        dc:"排序"`
	Status      int         `json:"status"      dc:"状态(1:正常 2:停用)"`
	CreatedAt   *gtime.Time `json:"createdAt"   dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   dc:"修改时间"`
}

// ContactGroupStatusInp 更新通讯录分组状态
type ContactGroupStatusInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" v:"required|in:1,2#状态不能为空|状态值不正确" dc:"状态(1:正常 2:停用)"`
}

func (in *ContactGroupStatusInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactGroupStatusModel struct{}
