// Package adminin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package adminin

import (
	"context"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ContactUpdateFieldsInp 修改通讯录联系人字段过滤
type ContactUpdateFieldsInp struct {
	Name       string `json:"name"       description:"姓名"`
	Mobile     string `json:"mobile"     description:"手机号码"`
	Company    string `json:"company"    description:"公司名称"`
	Position   string `json:"position"   description:"职位"`
	Department string `json:"department" description:"部门"`
	Email      string `json:"email"      description:"邮箱"`
	Remark     string `json:"remark"     description:"备注"`
	Sort       int    `json:"sort"       description:"排序"`
	Status     int    `json:"status"     description:"状态(1:正常 2:停用)"`
	UpdatedBy  int64  `json:"updatedBy"  description:"更新者"`
}

// ContactInsertFieldsInp 新增通讯录联系人字段过滤
type ContactInsertFieldsInp struct {
	MemberId   int64  `json:"memberId"   description:"用户ID"`
	Name       string `json:"name"       description:"姓名"`
	Mobile     string `json:"mobile"     description:"手机号码"`
	Company    string `json:"company"    description:"公司名称"`
	Position   string `json:"position"   description:"职位"`
	Department string `json:"department" description:"部门"`
	Email      string `json:"email"      description:"邮箱"`
	Remark     string `json:"remark"     description:"备注"`
	Sort       int    `json:"sort"       description:"排序"`
	Status     int    `json:"status"     description:"状态(1:正常 2:停用)"`
	CreatedBy  int64  `json:"createdBy"  description:"创建者"`
	UpdatedBy  int64  `json:"updatedBy"  description:"更新者"`
}

// ContactEditInp 修改/新增通讯录联系人
type ContactEditInp struct {
	entity.Contact
	GroupIds []int64 `json:"groupIds" dc:"分组ID列表"`
}

func (in *ContactEditInp) Filter(ctx context.Context) (err error) {
	// 验证姓名
	if err := g.Validator().Rules("required").Data(in.Name).Messages("姓名不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证手机号
	if err := g.Validator().Rules("required").Data(in.Mobile).Messages("手机号不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("phone").Data(in.Mobile).Messages("手机号格式不正确").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证邮箱
	if in.Email != "" {
		if err := g.Validator().Rules("email").Data(in.Email).Messages("邮箱格式不正确").Run(ctx); err != nil {
			return err.Current()
		}
	}

	return
}

type ContactEditModel struct{}

// ContactDeleteInp 删除通讯录联系人
type ContactDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *ContactDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactDeleteModel struct{}

// ContactViewInp 获取指定通讯录联系人信息
type ContactViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *ContactViewInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactViewModel struct {
	entity.Contact
	Groups         []GroupInfo       `json:"groups" dc:"所属分组"`
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
}

type GroupInfo struct {
	Id   int64  `json:"id" dc:"分组ID"`
	Name string `json:"name" dc:"分组名称"`
}

// ContactListInp 获取通讯录联系人列表
type ContactListInp struct {
	form.PageReq
	form.StatusReq
	Id         int64   `json:"id"         dc:"联系人ID"`
	Name       string  `json:"name"       dc:"姓名"`
	Mobile     string  `json:"mobile"     dc:"手机号码"`
	Company    string  `json:"company"    dc:"公司名称"`
	Position   string  `json:"position"   dc:"职位"`
	Department string  `json:"department" dc:"部门"`
	Email      string  `json:"email"      dc:"邮箱"`
	GroupId    int64   `json:"groupId"    dc:"分组ID（用于导出筛选）"`
	Keyword    string  `json:"keyword"    dc:"搜索关键词（姓名或手机号）"`
	CreatedAt  []int64 `json:"createdAt"  dc:"创建时间"`
}

func (in *ContactListInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactListModel struct {
	Id         int64       `json:"id"         dc:"联系人ID"`
	Name       string      `json:"name"       dc:"姓名"`
	Mobile     string      `json:"mobile"     dc:"手机号码"`
	Company    string      `json:"company"    dc:"公司名称"`
	Position   string      `json:"position"   dc:"职位"`
	Department string      `json:"department" dc:"部门"`
	Email      string      `json:"email"      dc:"邮箱"`
	Remark     string      `json:"remark"     dc:"备注"`
	Sort       int         `json:"sort"       dc:"排序"`
	Status     int         `json:"status"     dc:"状态(1:正常 2:停用)"`
	Groups     []GroupInfo `json:"groups"     dc:"所属分组"`
	CreatedAt  *gtime.Time `json:"createdAt"  dc:"创建时间"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  dc:"修改时间"`
}

// ContactExportModel 导出通讯录联系人
type ContactExportModel struct {
	Index      int         `json:"index"      dc:"序号"`
	Name       string      `json:"name"       dc:"姓名"`
	Mobile     string      `json:"mobile"     dc:"手机号码"`
	Company    string      `json:"company"    dc:"公司名称"`
	Position   string      `json:"position"   dc:"职位"`
	Department string      `json:"department" dc:"部门"`
	Email      string      `json:"email"      dc:"邮箱"`
	Remark     string      `json:"remark"     dc:"备注"`
	StatusText string      `json:"statusText" dc:"状态"`
	CreatedAt  *gtime.Time `json:"createdAt"  dc:"创建时间"`
}

// ContactStatusInp 更新通讯录联系人状态
type ContactStatusInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" v:"required|in:1,2#状态不能为空|状态值不正确" dc:"状态(1:正常 2:停用)"`
}

func (in *ContactStatusInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactStatusModel struct{}

// ContactListByGroupInp 按分组筛选联系人
type ContactListByGroupInp struct {
	form.PageReq
	form.StatusReq
	GroupId   int64   `json:"groupId"   v:"required#分组ID不能为空" dc:"分组ID"`
	Keyword   string  `json:"keyword"   dc:"搜索关键词（姓名或手机号）"`
	CreatedAt []int64 `json:"createdAt" dc:"创建时间"`
}

func (in *ContactListByGroupInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactWithGroupsModel struct {
	entity.Contact
	Groups []GroupInfo `json:"groups" dc:"所属分组"`
}

// ContactUpdateGroupsInp 更新联系人分组
type ContactUpdateGroupsInp struct {
	ContactId int64   `json:"contactId" v:"required#联系人ID不能为空" dc:"联系人ID"`
	GroupIds  []int64 `json:"groupIds" v:"" dc:"分组ID列表"`
}

func (in *ContactUpdateGroupsInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactUpdateGroupsModel struct{}

// ContactImportInp 批量导入联系人
type ContactImportInp struct {
	FileUrl      string `json:"fileUrl" v:"required#文件路径不能为空" dc:"文件路径"`
	AttachmentId int64  `json:"attachmentId" v:"required#附件ID不能为空" dc:"附件ID"`
}

func (in *ContactImportInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactImportModel struct{}

// ContactCheckImportFileInp 检查导入文件
type ContactCheckImportFileInp struct {
	FileUrl      string `json:"fileUrl" v:"required#文件路径不能为空" dc:"文件路径"`
	AttachmentId int64  `json:"attachmentId" v:"required#附件ID不能为空" dc:"附件ID"`
}

func (in *ContactCheckImportFileInp) Filter(ctx context.Context) (err error) {
	return
}

type ContactCheckImportFileModel struct {
	SuccessCount   int    `json:"successCount" dc:"成功条数"`
	FailCount      int    `json:"failCount" dc:"失败条数"`
	DuplicateCount int    `json:"duplicateCount" dc:"重复条数"`
	NewFileUrl     string `json:"newFileUrl" dc:"新文件路径"`
}
