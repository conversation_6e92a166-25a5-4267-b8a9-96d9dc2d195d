// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/consts"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	"hotgo/utility/tree"
	"hotgo/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SmsChannelUpdateFields 修改短信通道字段过滤
type SmsChannelUpdateFields struct {
	Pid         int64   `json:"pid"         dc:"上级通道"`
	ChannelName string  `json:"channelName" dc:"通道名称"`
	Protocol    int     `json:"protocol"    dc:"通道协议"`
	Flag        int     `json:"flag"        dc:"运营商标识"`
	UnitPrice   float64 `json:"unitPrice"   dc:"短信单价"`
	Host        string  `json:"host"        dc:"接口地址"`
	Account     string  `json:"account"     dc:"接口账号"`
	Passwd      string  `json:"passwd"      dc:"接口密码"`
	Params      string  `json:"params"      dc:"接口参数"`
	ExtCode     string  `json:"extCode"     dc:"扩展码"`
	Description string  `json:"description" dc:"描述"`
	Level       int     `json:"level"       dc:"关系树级别"`
	Tree        string  `json:"tree"        dc:"关系树"`
	Sort        int     `json:"sort"        dc:"排序"`
	Status      int     `json:"status"      dc:"状态"`
	UpdatedBy   int64   `json:"updatedBy"   dc:"更新者"`
	ChannelType string  `json:"channelType"        dc:"通道类型"`
}

// SmsChannelInsertFields 新增短信通道字段过滤
type SmsChannelInsertFields struct {
	Pid         int64   `json:"pid"         dc:"上级通道"`
	ChannelName string  `json:"channelName" dc:"通道名称"`
	Protocol    int     `json:"protocol"    dc:"通道协议"`
	Flag        int     `json:"flag"        dc:"运营商标识"`
	UnitPrice   float64 `json:"unitPrice"   dc:"短信单价"`
	Host        string  `json:"host"        dc:"接口地址"`
	Account     string  `json:"account"     dc:"接口账号"`
	Passwd      string  `json:"passwd"      dc:"接口密码"`
	Params      string  `json:"params"      dc:"接口参数"`
	ExtCode     string  `json:"extCode"     dc:"扩展码"`
	Description string  `json:"description" dc:"描述"`
	Level       int     `json:"level"       dc:"关系树级别"`
	Tree        string  `json:"tree"        dc:"关系树"`
	Sort        int     `json:"sort"        dc:"排序"`
	Status      int     `json:"status"      dc:"状态"`
	CreatedBy   int64   `json:"createdBy"   dc:"创建者"`
	ChannelType string  `json:"channelType"        dc:"通道类型"`
}

// SmsChannelEditInp 修改/新增短信通道
type SmsChannelEditInp struct {
	entity.SysSmsChannel
}

func (in *SmsChannelEditInp) Filter(ctx context.Context) (err error) {
	// 验证通道名称
	if err := g.Validator().Rules("required").Data(in.ChannelName).Messages("通道名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证通道协议
	//if err := g.Validator().Rules("required").Data(in.Protocol).Messages("通道协议不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}
	//if err := g.Validator().Rules("in:0,1,2,3").Data(in.Protocol).Messages("通道协议值不正确").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证运营商标识
	//if err := g.Validator().Rules("required").Data(in.Flag).Messages("运营商标识不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}
	//if err := g.Validator().Rules("in:0,1,2,3").Data(in.Flag).Messages("运营商标识值不正确").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证短信单价
	if err := g.Validator().Rules("required").Data(in.UnitPrice).Messages("短信单价不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type SmsChannelEditModel struct{}

// SmsChannelDeleteInp 删除短信通道
type SmsChannelDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *SmsChannelDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type SmsChannelDeleteModel struct{}

// SmsChannelViewInp 获取指定短信通道信息
type SmsChannelViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *SmsChannelViewInp) Filter(ctx context.Context) (err error) {
	return
}

type SmsChannelViewModel struct {
	entity.SysSmsChannel
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
}

// SmsChannelListInp 获取短信通道列表
type SmsChannelListInp struct {
	form.PageReq
	Pid         int64         `json:"pid"         dc:"上级通道"`
	ChannelName string        `json:"channelName" dc:"通道名称"`
	Status      int           `json:"status"      dc:"状态"`
	CreatedAt   []*gtime.Time `json:"createdAt"   dc:"创建时间"`
}

func (in *SmsChannelListInp) Filter(ctx context.Context) (err error) {
	return
}

type SmsChannelListModel struct {
	Pid            int64             `json:"pid"            dc:"上级通道"`
	Id             int64             `json:"id"             dc:"ID"`
	ChannelName    string            `json:"channelName"    dc:"通道名称"`
	Protocol       int               `json:"protocol"       dc:"通道协议"`
	Flag           int               `json:"flag"           dc:"运营商标识"`
	UnitPrice      float64           `json:"unitPrice"      dc:"短信单价"`
	Host           string            `json:"host"           dc:"接口地址"`
	Account        string            `json:"account"     dc:"接口账号"`
	Passwd         string            `json:"passwd"      dc:"接口密码"`
	Params         string            `json:"params"      dc:"接口参数"`
	ExtCode        string            `json:"extCode"     dc:"扩展码"`
	Description    string            `json:"description"    dc:"描述"`
	Sort           int               `json:"sort"           dc:"排序"`
	Status         int               `json:"status"         dc:"状态"`
	CreatedBy      int64             `json:"createdBy"      dc:"创建者"`
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBy      int64             `json:"updatedBy"      dc:"更新者"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
	CreatedAt      *gtime.Time       `json:"createdAt"      dc:"创建时间"`
	UpdatedAt      *gtime.Time       `json:"updatedAt"      dc:"修改时间"`
	ChannelType    string            `json:"channelType"    dc:"通道类型"`
}

// SmsChannelExportModel 导出短信通道
type SmsChannelExportModel struct {
	Pid         int64       `json:"pid"         dc:"上级通道"`
	Id          int64       `json:"id"          dc:"ID"`
	ChannelName string      `json:"channelName" dc:"通道名称"`
	Protocol    int         `json:"protocol"    dc:"通道协议"`
	Flag        int         `json:"flag"        dc:"运营商标识"`
	UnitPrice   float64     `json:"unitPrice"   dc:"短信单价"`
	Host        string      `json:"host"        dc:"接口地址"`
	Account     string      `json:"account"     dc:"接口账号"`
	Passwd      string      `json:"passwd"      dc:"接口密码"`
	Params      string      `json:"params"      dc:"接口参数"`
	ExtCode     string      `json:"extCode"     dc:"扩展码"`
	Description string      `json:"description" dc:"描述"`
	Status      int         `json:"status"      dc:"状态"`
	CreatedBy   int64       `json:"createdBy"   dc:"创建者"`
	UpdatedBy   int64       `json:"updatedBy"   dc:"更新者"`
	CreatedAt   *gtime.Time `json:"createdAt"   dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   dc:"修改时间"`
	ChannelType string      `json:"channelType" dc:"通道类型"`
}

// SmsChannelMaxSortInp 获取短信通道最大排序
type SmsChannelMaxSortInp struct{}

func (in *SmsChannelMaxSortInp) Filter(ctx context.Context) (err error) {
	return
}

type SmsChannelMaxSortModel struct {
	Sort int `json:"sort"  description:"排序"`
}

// SmsChannelStatusInp 更新短信通道状态
type SmsChannelStatusInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" dc:"状态"`
}

func (in *SmsChannelStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type SmsChannelStatusModel struct{}

// SmsChannelTreeOption 关系树选项
type SmsChannelTreeOption struct {
	Pid         int64       `json:"pid"         dc:"上级通道"`
	Id          int64       `json:"id"          dc:"ID"`
	ChannelName string      `json:"channelName" dc:"通道名称"`
	Children    []tree.Node `json:"children"  dc:"子节点"`
}

// ID 获取节点ID
func (t *SmsChannelTreeOption) ID() int64 {
	return t.Id
}

// PID 获取父级节点ID
func (t *SmsChannelTreeOption) PID() int64 {
	return t.Pid
}

// SetChildren 设置子节点数据
func (t *SmsChannelTreeOption) SetChildren(children []tree.Node) {
	t.Children = children
}
