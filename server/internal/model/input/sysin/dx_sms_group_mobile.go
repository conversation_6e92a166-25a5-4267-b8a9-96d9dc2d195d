// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupMobileUpdateFields 修改群发号码字段过滤
type DxSmsGroupMobileUpdateFields struct {
	MemberId   int64       `json:"memberId"   dc:"户用ID"`
	GroupId    int64       `json:"groupId"    dc:"群发ID"`
	Mobile     string      `json:"mobile"     dc:"手机号码"`
	TplVars    *gjson.Json `json:"tplVars"    dc:"模板变量"`
	SendStatus int         `json:"sendStatus" dc:"发送状态"`
}

// DxSmsGroupMobileInsertFields 新增群发号码字段过滤
type DxSmsGroupMobileInsertFields struct {
	MemberId   int64       `json:"memberId"   dc:"户用ID"`
	GroupId    int64       `json:"groupId"    dc:"群发ID"`
	Mobile     string      `json:"mobile"     dc:"手机号码"`
	TplVars    *gjson.Json `json:"tplVars"    dc:"模板变量"`
	SendStatus int         `json:"sendStatus" dc:"发送状态"`
}

// DxSmsGroupMobileEditInp 修改/新增群发号码
type DxSmsGroupMobileEditInp struct {
	entity.DxSmsGroupMobile
}

func (in *DxSmsGroupMobileEditInp) Filter(ctx context.Context) (err error) {
	// 验证手机号码
	if err := g.Validator().Rules("phone").Data(in.Mobile).Messages("手机号码不是手机号码").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type DxSmsGroupMobileEditModel struct{}

// DxSmsGroupMobileDeleteInp 删除群发号码
type DxSmsGroupMobileDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsGroupMobileDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupMobileDeleteModel struct{}

// DxSmsGroupMobileViewInp 获取指定群发号码信息
type DxSmsGroupMobileViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsGroupMobileViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupMobileViewModel struct {
	entity.DxSmsGroupMobile
}

// DxSmsGroupMobileListInp 获取群发号码列表
type DxSmsGroupMobileListInp struct {
	form.PageReq
	Id                    int64         `json:"id"                    dc:"ID"`
	Mobile                string        `json:"mobile"                dc:"手机号码"`
	SendStatus            int           `json:"sendStatus"            dc:"发送状态"`
	CreatedAt             []*gtime.Time `json:"createdAt"             dc:"创建时间"`
	DxSmsGroupSendBatchNo string        `json:"dxSmsGroupSendBatchNo" dc:"批次号"`
}

func (in *DxSmsGroupMobileListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupMobileListModel struct {
	Id                    int64       `json:"id"                    dc:"ID"`
	MemberId              int64       `json:"memberId"              dc:"户用ID"`
	GroupId               int64       `json:"groupId"               dc:"群发ID"`
	Mobile                string      `json:"mobile"                dc:"手机号码"`
	TplVars               *gjson.Json `json:"tplVars"               dc:"模板变量"`
	SendStatus            int         `json:"sendStatus"            dc:"发送状态"`
	CreatedAt             *gtime.Time `json:"createdAt"             dc:"创建时间"`
	DxSmsGroupSendBatchNo string      `json:"dxSmsGroupSendBatchNo" dc:"批次号"`
}

// DxSmsGroupMobileExportModel 导出群发号码
type DxSmsGroupMobileExportModel struct {
	Id                    int64       `json:"id"                    dc:"ID"`
	MemberId              int64       `json:"memberId"              dc:"户用ID"`
	GroupId               int64       `json:"groupId"               dc:"群发ID"`
	Mobile                string      `json:"mobile"                dc:"手机号码"`
	TplVars               *gjson.Json `json:"tplVars"               dc:"模板变量"`
	SendStatus            int         `json:"sendStatus"            dc:"发送状态"`
	CreatedAt             *gtime.Time `json:"createdAt"             dc:"创建时间"`
	DxSmsGroupSendBatchNo string      `json:"dxSmsGroupSendBatchNo" dc:"批次号"`
}