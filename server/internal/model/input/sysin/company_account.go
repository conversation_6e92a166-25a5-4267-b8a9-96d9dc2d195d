// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CompanyAccountUpdateFields 修改企业账户字段过滤
type CompanyAccountUpdateFields struct {
	Pid                int64       `json:"pid"                dc:"父ID"`
	DeptId             int64       `json:"deptId"             dc:"部门ID"`
	MemberId           int64       `json:"memberId"           dc:"用户ID"`
	CompanyName        string      `json:"companyName"        dc:"名称"`
	CompanyAccount     string      `json:"companyAccount"     dc:"账号"`
	Leader             string      `json:"leader"             dc:"负责人"`
	Phone              string      `json:"phone"              dc:"联系电话"`
	Email              string      `json:"email"              dc:"邮箱"`
	Address            string      `json:"address"            dc:"地址"`
	Attachfiles        *gjson.Json `json:"attachfiles"        dc:"附件列表"`
	ChannelId          int64       `json:"channelId"          dc:"短信通道id"`
	SmsType            int         `json:"smsType"            dc:"短信类型"`
	GroupSendAuditMode int         `json:"groupSendAuditMode" dc:"群发鉴权模式"`
	TemplateAuditMode  int         `json:"templateAuditMode"  dc:"模板鉴权模式"`
	SecretKey          string      `json:"secretKey"          dc:"secret_key"`
	Sort               int         `json:"sort"               dc:"sort"`
}

// CompanyAccountInsertFields 新增企业账户字段过滤
type CompanyAccountInsertFields struct {
	Pid                int64       `json:"pid"                dc:"父ID"`
	DeptId             int64       `json:"deptId"             dc:"部门ID"`
	MemberId           int64       `json:"memberId"           dc:"用户ID"`
	CompanyName        string      `json:"companyName"        dc:"名称"`
	CompanyAccount     string      `json:"companyAccount"     dc:"账号"`
	Leader             string      `json:"leader"             dc:"负责人"`
	Phone              string      `json:"phone"              dc:"联系电话"`
	Email              string      `json:"email"              dc:"邮箱"`
	Address            string      `json:"address"            dc:"地址"`
	Attachfiles        *gjson.Json `json:"attachfiles"        dc:"附件列表"`
	ChannelId          int64       `json:"channelId"          dc:"短信通道id"`
	SmsType            int         `json:"smsType"            dc:"短信类型"`
	GroupSendAuditMode int         `json:"groupSendAuditMode" dc:"群发鉴权模式"`
	TemplateAuditMode  int         `json:"templateAuditMode"  dc:"模板鉴权模式"`
	SecretKey          string      `json:"secretKey"          dc:"secret_key"`
	Sort               int         `json:"sort"               dc:"sort"`
}

// CompanyAccountEditInp 修改/新增企业账户
type CompanyAccountEditInp struct {
	entity.AdminCompany
	Password string `json:"password" dc:"登录密码"`
}

func (in *CompanyAccountEditInp) Filter(ctx context.Context) (err error) {
	// 验证企业名称
	if err := g.Validator().Rules("required").Data(in.CompanyName).Messages("企业名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证企业账号
	if err := g.Validator().Rules("required").Data(in.CompanyAccount).Messages("企业账号不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 新增时验证登录密码
	if in.Id == 0 {
		if err := g.Validator().Rules("required").Data(in.Password).Messages("登录密码不能为空").Run(ctx); err != nil {
			return err.Current()
		}
	}

	// 验证邮箱格式（如果有邮箱）
	if in.Email != "" {
		if err := g.Validator().Rules("email").Data(in.Email).Messages("邮箱格式不正确").Run(ctx); err != nil {
			return err.Current()
		}
	}

	return
}

type CompanyAccountEditModel struct{}

// CompanyAccountDeleteInp 删除企业账户
type CompanyAccountDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *CompanyAccountDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanyAccountDeleteModel struct{}

// CompanyAccountViewInp 获取指定企业账户信息
type CompanyAccountViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *CompanyAccountViewInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanyAccountViewModel struct {
	entity.AdminCompany
}

// CompanyAccountListInp 获取企业账户列表
type CompanyAccountListInp struct {
	form.PageReq
	Id                   int64       `json:"id"                   dc:"ID"`
	CompanyName          string      `json:"companyName"          dc:"企业名称"`
	AdminMemberUsername  string      `json:"adminMemberUsername"  dc:"企业帐号"`
	AdminMemberStatus    int         `json:"adminMemberStatus"    dc:"状态"`
	AdminMemberCreatedAt *gtime.Time `json:"adminMemberCreatedAt" dc:"创建时间"`
}

func (in *CompanyAccountListInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanyAccountListModel struct {
	Id                   int64       `json:"id"                   dc:"ID"`
	MemberId             int64       `json:"memberId"             dc:"用户ID"`
	CompanyName          string      `json:"companyName"          dc:"名称"`
	CompanyAccount       string      `json:"companyAccount"       dc:"账号"`
	Balance              float64     `json:"balance"              dc:"余额"`
	SmsBalance           int         `json:"smsBalance"           dc:"短信余额"`
	Leader               string      `json:"leader"               dc:"负责人"`
	Phone                string      `json:"phone"                dc:"联系电话"`
	Email                string      `json:"email"                dc:"邮箱"`
	ChannelId            int64       `json:"channelId"            dc:"短信通道id"`
	SmsType              int         `json:"smsType"              dc:"短信类型"`
	GroupSendAuditMode   int         `json:"groupSendAuditMode"   dc:"群发鉴权模式"`
	TemplateAuditMode    int         `json:"templateAuditMode"    dc:"模板鉴权模式"`
	SecretKey            string      `json:"secretKey"            dc:"secret_key"`
	Sort                 int         `json:"sort"                 dc:"sort"`
	SmsAmount            int         `json:"smsAmount"            dc:"短信总额"`
	AdminMemberStatus    int         `json:"adminMemberStatus"    dc:"状态"`
	AdminMemberCreatedAt *gtime.Time `json:"adminMemberCreatedAt" dc:"创建时间"`
}

// CompanyAccountExportModel 导出企业账户
type CompanyAccountExportModel struct {
	Id                   int64       `json:"id"                   dc:"ID"`
	MemberId             int64       `json:"memberId"             dc:"用户ID"`
	CompanyName          string      `json:"companyName"          dc:"名称"`
	CompanyAccount       string      `json:"companyAccount"       dc:"账号"`
	Balance              float64     `json:"balance"              dc:"余额"`
	SmsBalance           int         `json:"smsBalance"           dc:"短信余额"`
	Leader               string      `json:"leader"               dc:"负责人"`
	Phone                string      `json:"phone"                dc:"联系电话"`
	Email                string      `json:"email"                dc:"邮箱"`
	ChannelId            int64       `json:"channelId"            dc:"短信通道id"`
	SmsType              int         `json:"smsType"              dc:"短信类型"`
	GroupSendAuditMode   int         `json:"groupSendAuditMode"   dc:"群发鉴权模式"`
	TemplateAuditMode    int         `json:"templateAuditMode"    dc:"模板鉴权模式"`
	SecretKey            string      `json:"secretKey"            dc:"secret_key"`
	Sort                 int         `json:"sort"                 dc:"sort"`
	SmsAmount            int         `json:"smsAmount"            dc:"短信总额"`
	AdminMemberStatus    int         `json:"adminMemberStatus"    dc:"状态"`
	AdminMemberCreatedAt *gtime.Time `json:"adminMemberCreatedAt" dc:"创建时间"`
}

// CompanyAccountMaxSortInp 获取企业账户最大排序
type CompanyAccountMaxSortInp struct{}

func (in *CompanyAccountMaxSortInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanyAccountMaxSortModel struct {
	Sort int `json:"sort"  description:"排序"`
}
