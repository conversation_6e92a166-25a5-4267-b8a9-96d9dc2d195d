// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package sysin

import (
	"context"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsFailureLogListInp 获取失败重发明细列表
type DxSmsFailureLogListInp struct {
	form.PageReq
	OriginalRecordId int64 `json:"originalRecordId" v:"required#原始记录ID不能为空" dc:"原始记录ID"`
}

func (in *DxSmsFailureLogListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsFailureLogListModel struct {
	Id               int64       `json:"id"              dc:"ID"`
	OriginalRecordId int64       `json:"originalRecordId" dc:"原始记录ID"`
	MemberId         int64       `json:"memberId"        dc:"用户ID"`
	BatchNo          string      `json:"batchNo"         dc:"批次号"`
	Mobile           string      `json:"mobile"          dc:"手机号码"`
	ChannelId        int64       `json:"channelId"       dc:"通道ID"`
	FailedReason     string      `json:"failedReason"    dc:"失败原因"`
	FailedTime       *gtime.Time `json:"failedTime"      dc:"失败时间"`
	CreatedAt        *gtime.Time `json:"createdAt"       dc:"创建时间"`
	// 关联字段
	ChannelName string `json:"channelName"     dc:"通道名称"`
}

// DxSmsFailureLogAddInp 新增失败重发明细
type DxSmsFailureLogAddInp struct {
	entity.DxSmsFailureLog
}

func (in *DxSmsFailureLogAddInp) Filter(ctx context.Context) (err error) {
	return
}
