// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"fmt"
	"hotgo/internal/consts"
	"hotgo/internal/library/contexts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	"hotgo/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"hotgo/utility/tree"
)

// AdminCompanyUpdateFields 修改企业列表字段过滤
type AdminCompanyUpdateFields struct {
	Pid                int64       `json:"pid"                dc:"父ID"`
	DeptId             int64       `json:"deptId"             dc:"部门ID"`
	MemberId           int64       `json:"memberId"           dc:"用户ID"`
	CompanyName        string      `json:"companyName"        dc:"名称"`
	CompanyAccount     string      `json:"companyAccount"     dc:"账号"`
	Leader             string      `json:"leader"             dc:"负责人"`
	Phone              string      `json:"phone"              dc:"联系电话"`
	Email              string      `json:"email"              dc:"邮箱"`
	Address            string      `json:"address"            dc:"地址"`
	Attachfiles        *gjson.Json `json:"attachfiles"        dc:"附件列表"`
	ChannelId          int         `json:"channelId"          dc:"短信通道"`
	SmsType            int         `json:"smsType"            dc:"短信类型"`
	GroupSendAuditMode int         `json:"groupSendAuditMode" dc:"群发鉴权模式"`
	TemplateAuditMode  int         `json:"templateAuditMode"  dc:"模板鉴权模式"`
	SecretKey          string      `json:"secretKey"          dc:"secret_key"`
	Level              int         `json:"level"              dc:"level"`
	Tree               string      `json:"tree"               dc:"tree"`
	Sort               int         `json:"sort"               dc:"sort"`
}

// AdminCompanyInsertFields 新增企业列表字段过滤
type AdminCompanyInsertFields struct {
	Pid                int64       `json:"pid"                dc:"父ID"`
	DeptId             int64       `json:"deptId"             dc:"部门ID"`
	MemberId           int64       `json:"memberId"           dc:"用户ID"`
	CompanyName        string      `json:"companyName"        dc:"名称"`
	CompanyAccount     string      `json:"companyAccount"     dc:"账号"`
	Leader             string      `json:"leader"             dc:"负责人"`
	Phone              string      `json:"phone"              dc:"联系电话"`
	Email              string      `json:"email"              dc:"邮箱"`
	Address            string      `json:"address"            dc:"地址"`
	Attachfiles        *gjson.Json `json:"attachfiles"        dc:"附件列表"`
	ChannelId          int         `json:"channelId"          dc:"短信通道"`
	SmsType            int         `json:"smsType"            dc:"短信类型"`
	GroupSendAuditMode int         `json:"groupSendAuditMode" dc:"群发鉴权模式"`
	TemplateAuditMode  int         `json:"templateAuditMode"  dc:"模板鉴权模式"`
	SecretKey          string      `json:"secretKey"          dc:"secret_key"`
	Level              int         `json:"level"              dc:"level"`
	Tree               string      `json:"tree"               dc:"tree"`
	Sort               int         `json:"sort"               dc:"sort"`
}

// AdminCompanyEditInp 修改/新增企业列表
// ResendChannelConfig 重发通道配置
type ResendChannelConfig struct {
	ChannelId int64 `json:"channelId" dc:"通道ID"`
	Priority  int   `json:"priority" dc:"优先级"`
}

type AdminCompanyEditInp struct {
	entity.AdminCompany
	UserName             string                `json:"userName" dc:"企业账号"`
	Password             string                `json:"password" dc:"账号密码"`
	ResendChannels       []int64               `json:"resendChannels" dc:"补发通道列表"`       // 兼容原有简单格式
	ResendChannelConfigs []ResendChannelConfig `json:"resendChannelConfigs" dc:"重发通道配置"` // 新的带优先级格式
}

func (in *AdminCompanyEditInp) Filter(ctx context.Context) (err error) {
	// 验证名称
	if err := g.Validator().Rules("required").Data(in.CompanyName).Messages("名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	// 验证账号
	if err := g.Validator().Rules("required").Data(in.UserName).Messages("账号不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	// 验证密码
	if in.Id == 0 {
		if err := g.Validator().Rules("required").Data(in.Password).Messages("密码不能为空").Run(ctx); err != nil {
			return err.Current()
		}
	}

	// 验证通道
	if in.Pid > 0 {
		if err := g.Validator().Rules("min:1").Data(in.ChannelId).Messages("请选择短信通道").Run(ctx); err != nil {
			return err.Current()
		}
	}

	return
}

type AdminCompanyEditModel struct{}

// AdminCompanyDeleteInp 删除企业列表
type AdminCompanyDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *AdminCompanyDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type AdminCompanyDeleteModel struct{}

// AdminCompanyViewInp 获取指定企业列表信息
type AdminCompanyViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *AdminCompanyViewInp) Filter(ctx context.Context) (err error) {
	return
}

// ResendChannelInfo 重发通道信息
type ResendChannelInfo struct {
	ChannelId   int64  `json:"channelId" dc:"通道ID"`
	ChannelName string `json:"channelName" dc:"通道名称"`
	Priority    int    `json:"priority" dc:"优先级"`
}

type AdminCompanyViewModel struct {
	entity.AdminCompany
	UserName           string              `json:"userName"`
	SmsChannelName     string              `json:"smsChannelName" orm:"channel_name"`
	ResendChannels     []int64             `json:"resendChannels" dc:"补发通道列表"`
	ResendChannelInfos []ResendChannelInfo `json:"resendChannelInfos" dc:"重发通道详细信息"`
}

// AdminCompanyListInp 获取企业列表列表
type AdminCompanyListInp struct {
	form.PageReq
	Id                   int64       `json:"id"                   dc:"ID"`
	Pid                  int64       `json:"pid"                  dc:"父ID"`
	CompanyName          string      `json:"companyName"  dc:"企业名称"`
	AdminMemberUsername  string      `json:"adminMemberUsername"  dc:"帐号"`
	AdminMemberPid       int64       `json:"adminMemberPid"       dc:"上级管理员ID"`
	AdminMemberStatus    int         `json:"adminMemberStatus"    dc:"状态"`
	AdminMemberCreatedAt *gtime.Time `json:"adminMemberCreatedAt" dc:"创建时间"`
}

func (in *AdminCompanyListInp) Filter(ctx context.Context) (err error) {
	return
}

type AdminCompanyListModel struct {
	Id                   int64       `json:"id"                   dc:"ID"`
	Pid                  int64       `json:"pid"                  dc:"父ID"`
	CompanyName          string      `json:"companyName"          dc:"名称"`
	CompanyAccount       string      `json:"companyAccount"     dc:"账号"`
	Balance              float64     `json:"balance"              dc:"余额"`
	SmsBalance           int         `json:"smsBalance"           dc:"短信余额"`
	SmsFrozen            int         `json:"smsFrozen"            dc:"短信冻结"`
	SmsAmount            int         `json:"smsAmount"            dc:"短信总额"`
	Leader               string      `json:"leader"               dc:"负责人"`
	Phone                string      `json:"phone"                dc:"联系电话"`
	Email                string      `json:"email"                dc:"邮箱"`
	Address              string      `json:"address"              dc:"地址"`
	ChannelId            int         `json:"channelId"            dc:"短信通道"`
	SmsType              int         `json:"smsType"              dc:"短信类型"`
	GroupSendAuditMode   int         `json:"groupSendAuditMode"   dc:"群发鉴权模式"`
	TemplateAuditMode    int         `json:"templateAuditMode"    dc:"模板鉴权模式"`
	Sort                 int         `json:"sort"                 dc:"sort"`
	AdminMemberUsername  string      `json:"adminMemberUsername"  dc:"帐号"`
	AdminMemberPid       int64       `json:"adminMemberPid"       dc:"上级管理员ID"`
	AdminMemberStatus    int         `json:"adminMemberStatus"    dc:"状态"`
	AdminMemberCreatedAt *gtime.Time `json:"adminMemberCreatedAt" dc:"创建时间"`
}

// AdminCompanyExportModel 导出企业列表
type AdminCompanyExportModel struct {
	Id                   int64       `json:"id"                   dc:"ID"`
	CompanyName          string      `json:"companyName"          dc:"名称"`
	CompanyAccount       string      `json:"companyAccount"     dc:"账号"`
	Balance              float64     `json:"balance"              dc:"余额"`
	SmsBalance           int         `json:"smsBalance"           dc:"短信余额"`
	SmsFrozen            int         `json:"smsFrozen"            dc:"短信冻结"`
	SmsAmount            int         `json:"smsAmount"            dc:"短信总额"`
	Leader               string      `json:"leader"               dc:"负责人"`
	Phone                string      `json:"phone"                dc:"联系电话"`
	Email                string      `json:"email"                dc:"邮箱"`
	Address              string      `json:"address"              dc:"地址"`
	ChannelId            int         `json:"channelId"            dc:"短信通道"`
	SmsType              int         `json:"smsType"              dc:"短信类型"`
	GroupSendAuditMode   int         `json:"groupSendAuditMode"   dc:"群发鉴权模式"`
	TemplateAuditMode    int         `json:"templateAuditMode"    dc:"模板鉴权模式"`
	Sort                 int         `json:"sort"                 dc:"sort"`
	AdminMemberUsername  string      `json:"adminMemberUsername"  dc:"帐号"`
	AdminMemberStatus    int         `json:"adminMemberStatus"    dc:"状态"`
	AdminMemberCreatedAt *gtime.Time `json:"adminMemberCreatedAt" dc:"创建时间"`
}

// AdminCompanyMaxSortInp 获取企业列表最大排序
type AdminCompanyMaxSortInp struct{}

func (in *AdminCompanyMaxSortInp) Filter(ctx context.Context) (err error) {
	return
}

type AdminCompanyMaxSortModel struct {
	Sort int `json:"sort"  description:"排序"`
}

// AdminCompanyTreeOption 关系树选项
type AdminCompanyTreeOption struct {
	Id             int64       `json:"id"             dc:"ID"`
	Pid            int64       `json:"pid"            dc:"父ID"`
	CompanyName    string      `json:"companyName"    dc:"名称"`
	AdminMemberId  int64       `json:"adminMemberId"  dc:"管理员ID"`
	AdminMemberPid int64       `json:"adminMemberPid" dc:"上级管理员ID"`
	Children       []tree.Node `json:"children"  dc:"子节点"`
}

// ID 获取节点ID
func (t *AdminCompanyTreeOption) ID() int64 {
	return t.Id
}

// PID 获取父级节点ID
func (t *AdminCompanyTreeOption) PID() int64 {
	return t.Pid
}

// SetChildren 设置子节点数据
func (t *AdminCompanyTreeOption) SetChildren(children []tree.Node) {
	t.Children = children
}

// AdminCompanyStatusInp 更新短信签名状态
type AdminCompanyStatusInp struct {
	Id                int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	AdminMemberStatus int   `json:"adminMemberStatus" dc:"状态"`
}

func (in *AdminCompanyStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}

	if in.AdminMemberStatus <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.AdminMemberStatus) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type AdminCompanyStatusModel struct{}

// AdminCompanyAddBalanceInp  增加余额
type AdminCompanyAddBalanceInp struct {
	Id               int64   `json:"id"          v:"required#用户ID不能为空"         dc:"管理员ID"`
	OperateMode      int64   `json:"operateMode"      v:"in:1,2#输入的操作方式是无效的"     dc:"操作方式"`
	Num              float64 `json:"num"                dc:"操作数量"`
	AppId            string  `json:"appId"`
	AddonsName       string  `json:"addonsName"`
	SelfNum          float64 `json:"selfNum"`
	SelfCreditGroup  string  `json:"selfCreditGroup"`
	OtherNum         float64 `json:"otherNum"`
	OtherCreditGroup string  `json:"otherCreditGroup"`
	Remark           string  `json:"remark"`
}

func (in *AdminCompanyAddBalanceInp) Filter(ctx context.Context) (err error) {
	if in.Num <= 0 {
		err = gerror.New("操作数量必须大于0")
		return
	}

	if in.OperateMode == 1 {
		// 加款
		in.SelfNum = -in.Num
		in.SelfCreditGroup = consts.CreditGroupOpIncr
		in.OtherNum = in.Num
		in.OtherCreditGroup = consts.CreditGroupIncr
		in.Remark = fmt.Sprintf("增加余额:%v", in.OtherNum)
	} else {
		// 扣款
		in.SelfNum = in.Num
		in.SelfCreditGroup = consts.CreditGroupOpDecr
		in.OtherNum = -in.Num
		in.OtherCreditGroup = consts.CreditGroupDecr
		in.Remark = fmt.Sprintf("扣除余额:%v", in.OtherNum)
	}

	in.AppId = contexts.GetModule(ctx)
	in.AddonsName = contexts.GetAddonName(ctx)
	return
}

type AdminCompanyAddBalanceModel struct{}

// AdminCompanyAddSMSBalanceInp  增加短信余额
type AdminCompanyAddSMSBalanceInp struct {
	Id               int64  `json:"id"          v:"required#用户ID不能为空"         dc:"管理员ID"`
	OperateMode      int64  `json:"operateMode"      v:"in:1,2#输入的操作方式是无效的"     dc:"操作方式"`
	Num              int    `json:"num"                dc:"操作数量"`
	AppId            string `json:"appId"`
	AddonsName       string `json:"addonsName"`
	SelfNum          int    `json:"selfNum"`
	SelfCreditGroup  string `json:"selfCreditGroup"`
	OtherNum         int    `json:"otherNum"`
	OtherCreditGroup string `json:"otherCreditGroup"`
	Remark           string `json:"remark"`
}

func (in *AdminCompanyAddSMSBalanceInp) Filter(ctx context.Context) (err error) {
	if in.Num <= 0 {
		err = gerror.New("操作数量必须大于0")
		return
	}

	if in.OperateMode == 1 {
		// 加款
		in.SelfNum = -in.Num
		in.SelfCreditGroup = consts.CreditGroupOpIncr
		in.OtherNum = in.Num
		in.OtherCreditGroup = consts.CreditGroupIncr
		in.Remark = fmt.Sprintf("增加短信:%v", in.OtherNum)
	} else {
		// 扣款
		in.SelfNum = in.Num
		in.SelfCreditGroup = consts.CreditGroupOpDecr
		in.OtherNum = -in.Num
		in.OtherCreditGroup = consts.CreditGroupDecr
		in.Remark = fmt.Sprintf("扣除短信:%v", in.OtherNum)
	}

	in.AppId = contexts.GetModule(ctx)
	in.AddonsName = contexts.GetAddonName(ctx)
	return
}

type AdminCompanyAddSMSBalanceModel struct{}

type AdminCompanyWorkbenchInp struct {
}

func (in *AdminCompanyWorkbenchInp) Filter(ctx context.Context) (err error) {
	return
}

type AdminCompanyWorkbenchModel struct {
	RechargeAmount  int `json:"rechargeAmount" dc:"充值量"`
	ConsumeAmount   int `json:"consumeAmount"  dc:"消费量"`
	RemainingAmount int `json:"remainingAmount" dc:"剩余量"`
}

// CompanyTreeInp 获取企业树形结构
type CompanyTreeInp struct {
	Status *uint `json:"status" dc:"状态"`
}

type CompanyTreeModel struct {
	Id       int64  `json:"id"        dc:"ID"`
	ParentId int64  `json:"parentId"  dc:"父ID"`
	Name     string `json:"name"      dc:"企业名称"`
	Status   uint   `json:"status"    dc:"状态"`
	Sort     int    `json:"sort"      dc:"排序"`
}

// CompanyTreeNode 企业树节点
type CompanyTreeNode struct {
	Id       int64             `json:"id"        dc:"ID"`
	ParentId int64             `json:"parentId"  dc:"父ID"`
	MemberId int64             `json:"memberId"  dc:"用户ID"`
	Name     string            `json:"name"      dc:"名称"`
	Children []CompanyTreeNode `json:"children"  dc:"子节点"`
}

func (n *CompanyTreeNode) ID() int64 {
	return n.Id
}

func (n *CompanyTreeNode) PID() int64 {
	return n.ParentId
}

func (n *CompanyTreeNode) SetChildren(children []tree.Node) {
	n.Children = make([]CompanyTreeNode, len(children))
	for i, child := range children {
		if node, ok := child.(*CompanyTreeNode); ok {
			n.Children[i] = *node
		}
	}
}
