// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsMoRecordViewInp 获取指定上行记录信息
type DxSmsMoRecordViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsMoRecordViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsMoRecordViewModel struct {
	entity.DxSmsMoRecord
}

// DxSmsMoRecordListInp 获取上行记录列表
type DxSmsMoRecordListInp struct {
	form.PageReq
	Ext                 string        `json:"ext"                 dc:"接入码"`
	CreatedAt           []*gtime.Time `json:"createdAt"           dc:"接收时间"`
	AdminMemberRealName string        `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string        `json:"adminMemberUsername" dc:"子账号"`
}

func (in *DxSmsMoRecordListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsMoRecordListModel struct {
	Id                  int64       `json:"id"                  dc:"ID"`
	Mobile              string      `json:"mobile"              dc:"接收号码"`
	Ext                 string      `json:"ext"                 dc:"接入码"`
	Msg                 string      `json:"msg"                 dc:"接收内容"`
	CreatedAt           *gtime.Time `json:"createdAt"           dc:"接收时间"`
	AdminMemberRealName string      `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string      `json:"adminMemberUsername" dc:"子账号"`
}

// DxSmsMoRecordExportModel 导出上行记录
type DxSmsMoRecordExportModel struct {
	AdminMemberRealName string      `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string      `json:"adminMemberUsername" dc:"子账号"`
	Ext                 string      `json:"ext"                 dc:"发送号码"`
	Mobile              string      `json:"mobile"              dc:"接收号码"`
	Msg                 string      `json:"msg"                 dc:"接收内容"`
	CreatedAt           *gtime.Time `json:"createdAt"           dc:"接收时间"`
}