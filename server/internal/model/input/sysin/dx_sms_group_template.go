// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/consts"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	"hotgo/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupTemplateUpdateFields 修改模板管理字段过滤
type DxSmsGroupTemplateUpdateFields struct {
	TplName     string `json:"tplName"    dc:"模板名称"`
	VarNum      int    `json:"varNum"     dc:"变量个数"`
	TplContent  string `json:"tplContent" dc:"模板内容"`
	Status      int    `json:"status"     dc:"状态"`
	AuditStatus int    `json:"auditStatus"     dc:"审核状态"`
}

// DxSmsGroupTemplateInsertFields 新增模板管理字段过滤
type DxSmsGroupTemplateInsertFields struct {
	MemberId    int64  `json:"memberId"   dc:"用户ID"`
	TplName     string `json:"tplName"    dc:"模板名称"`
	VarNum      int    `json:"varNum"     dc:"变量个数"`
	TplContent  string `json:"tplContent" dc:"模板内容"`
	AuditStatus int    `json:"auditStatus"         dc:"审核状态"`
	Status      int    `json:"status"     dc:"状态"`
	Remark      string `json:"remark"     dc:"备注"`
}

// DxSmsGroupTemplateEditInp 修改/新增模板管理
type DxSmsGroupTemplateEditInp struct {
	entity.DxSmsGroupTemplate
}

func (in *DxSmsGroupTemplateEditInp) Filter(ctx context.Context) (err error) {
	// 验证模板名称
	if err := g.Validator().Rules("required").Data(in.TplName).Messages("模板名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证变量个数
	if err := g.Validator().Rules("min:1").Data(in.VarNum).Messages("变量个数必须大于0").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证模板内容
	if err := g.Validator().Rules("required").Data(in.TplContent).Messages("模板内容不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证状态
	if err := g.Validator().Rules("required").Data(in.Status).Messages("状态不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2").Data(in.Status).Messages("状态值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type DxSmsGroupTemplateEditModel struct{}

// DxSmsGroupTemplateDeleteInp 删除模板管理
type DxSmsGroupTemplateDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsGroupTemplateDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupTemplateDeleteModel struct{}

// DxSmsGroupTemplateViewInp 获取指定模板管理信息
type DxSmsGroupTemplateViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsGroupTemplateViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupTemplateViewModel struct {
	entity.DxSmsGroupTemplate
}

// DxSmsGroupTemplateListInp 获取模板管理列表
type DxSmsGroupTemplateListInp struct {
	form.PageReq
	TplName             string        `json:"tplName"             dc:"模板名称"`
	Status              int           `json:"status"              dc:"状态"`
	AuditStatus         int           `json:"auditStatus"         dc:"审核状态"`
	Member              string        `json:"Member"   dc:"创建者"`
	CreatedAt           []*gtime.Time `json:"createdAt"           dc:"创建时间"`
	AdminMemberRealName string        `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string        `json:"adminMemberUsername" dc:"帐号"`
}

func (in *DxSmsGroupTemplateListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupTemplateListModel struct {
	Id            int64             `json:"id"                  dc:"ID"`
	MemberId      int64             `json:"memberId"   dc:"用户ID"`
	TplName       string            `json:"tplName"             dc:"模板名称"`
	VarNum        int               `json:"varNum"              dc:"变量个数"`
	TplContent    string            `json:"tplContent"          dc:"模板内容"`
	Status        int               `json:"status"              dc:"状态"`
	AuditStatus   int               `json:"auditStatus"         dc:"审核状态"`
	CreatedAt     *gtime.Time       `json:"createdAt"           dc:"创建时间"`
	Remark        string            `json:"remark"              dc:"备注"`
	MemberBySumma *hook.MemberSumma `json:"memberBySumma" dc:"创建人摘要信息"`
	AdminMemberRealName string            `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string            `json:"adminMemberUsername" dc:"帐号"`
}

// DxSmsGroupTemplateExportModel 导出模板管理
type DxSmsGroupTemplateExportModel struct {
	Id            int64             `json:"id"                  dc:"ID"`
	AdminMemberRealName string      `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string      `json:"adminMemberUsername" dc:"子帐号"`
	TplName       string            `json:"tplName"             dc:"模板名称"`
	VarNum        int               `json:"varNum"              dc:"变量个数"`
	TplContent    string            `json:"tplContent"          dc:"模板内容"`
	Status        int               `json:"status"              dc:"状态"`
	AuditStatus   int               `json:"auditStatus"         dc:"审核状态"`
	CreatedAt     *gtime.Time       `json:"createdAt"           dc:"创建时间"`
}

// DxSmsGroupTemplateStatusInp 更新模板管理状态
type DxSmsGroupTemplateStatusInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" dc:"状态"`
}

func (in *DxSmsGroupTemplateStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type DxSmsGroupTemplateStatusModel struct{}

// DxSmsGroupTemplateOptionInp 模板选项
type DxSmsGroupTemplateOptionInp struct{}

type DxSmsGroupTemplateOption struct {
	//Key   interface{} `json:"key"`
	Label   string      `json:"label"     dc:"标签"`
	Value   interface{} `json:"value"     dc:"键值"`
	Content string      `json:"content" dc:"模板内容"`
}
