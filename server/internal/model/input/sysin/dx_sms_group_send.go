// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsGroupSendUpdateFields 修改短信群发字段过滤
type DxSmsGroupSendUpdateFields struct {
	MemberId       int64       `json:"memberId"       dc:"用户ID"`
	BatchNo        string      `json:"batchNo"        dc:"批次号"`
	SmsMode        int         `json:"smsMode"        dc:"短信模式"`
	SendMode       int         `json:"sendMode"       dc:"发送模式"`
	SignId         int64       `json:"signId"         dc:"签名"`
	ChannelId      int64       `json:"channelId"      dc:"通道"`
	TemplateId     int64       `json:"templateId"     dc:"模板"`
	Content        string      `json:"content"        dc:"短信内容"`
	MobileNum      int         `json:"mobileNum"      dc:"号码数量"`
	Remark         string      `json:"remark"         dc:"备注"`
	SendStatus     int         `json:"sendStatus"     dc:"发送状态"`
	AuditStatus    int         `json:"auditStatus"    dc:"审核状态"`
	ScheduleTime   *gtime.Time `json:"scheduleTime"   dc:"定时发送时间"`
	CycleStartDate *gtime.Time `json:"cycleStartDate" dc:"周期发送开始日期"`
	CycleEndDate   *gtime.Time `json:"cycleEndDate"   dc:"周期发送结束日期"`
	CycleStartTime *gtime.Time `json:"cycleStartTime" dc:"周期发送开始时间"`
	CycleEndTime   *gtime.Time `json:"cycleEndTime"   dc:"周期发送结束时间"`
	CycleFreq      int         `json:"cycleFreq"      dc:"周期发送频次/天"`
	AttachmentId   int64       `json:"attachmentId"   dc:"附件ID"`
	FileUrl        string      `json:"fileUrl"        dc:"文件路径"`
	Mobiles        string      `json:"mobiles"        dc:"手机号码（手动输入）"`
	ContactGroups  string      `json:"contactGroups"  dc:"通讯录分组（JSON格式）"`
}

// DxSmsGroupSendInsertFields 新增短信群发字段过滤
type DxSmsGroupSendInsertFields struct {
	MemberId       int64       `json:"memberId"       dc:"用户ID"`
	BatchNo        string      `json:"batchNo"        dc:"批次号"`
	SmsMode        int         `json:"smsMode"        dc:"短信模式"`
	SendMode       int         `json:"sendMode"       dc:"发送模式"`
	SignId         int64       `json:"signId"         dc:"签名"`
	ChannelId      int64       `json:"channelId"      dc:"通道"`
	TemplateId     int64       `json:"templateId"     dc:"模板"`
	Content        string      `json:"content"        dc:"短信内容"`
	MobileNum      int         `json:"mobileNum"      dc:"号码数量"`
	Remark         string      `json:"remark"         dc:"备注"`
	SendStatus     int         `json:"sendStatus"     dc:"发送状态"`
	AuditStatus    int         `json:"auditStatus"    dc:"审核状态"`
	ScheduleTime   *gtime.Time `json:"scheduleTime"   dc:"定时发送时间"`
	CycleStartDate *gtime.Time `json:"cycleStartDate" dc:"周期发送开始日期"`
	CycleEndDate   *gtime.Time `json:"cycleEndDate"   dc:"周期发送结束日期"`
	CycleStartTime *gtime.Time `json:"cycleStartTime" dc:"周期发送开始时间"`
	CycleEndTime   *gtime.Time `json:"cycleEndTime"   dc:"周期发送结束时间"`
	CycleFreq      int         `json:"cycleFreq"      dc:"周期发送频次/天"`
	AttachmentId   int64       `json:"attachmentId"   dc:"附件ID"`
	FileUrl        string      `json:"fileUrl"        dc:"文件路径"`
	Mobiles        string      `json:"mobiles"        dc:"手机号码（手动输入）"`
	ContactGroups  string      `json:"contactGroups"  dc:"通讯录分组（JSON格式）"`
}

// DxSmsGroupSendEditInp 修改/新增短信群发
type DxSmsGroupSendEditInp struct {
	entity.DxSmsGroupSend
}

func (in *DxSmsGroupSendEditInp) Filter(ctx context.Context) (err error) {
	// 验证用户ID
	if err := g.Validator().Rules("required").Data(in.MemberId).Messages("用户ID不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证批次号
	//if err := g.Validator().Rules("required").Data(in.BatchNo).Messages("批次号不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证短信模式
	if err := g.Validator().Rules("required").Data(in.SmsMode).Messages("短信模式不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2,3").Data(in.SmsMode).Messages("短信模式值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证发送模式
	if err := g.Validator().Rules("required").Data(in.SendMode).Messages("发送模式不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2").Data(in.SendMode).Messages("发送模式值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证签名
	if err := g.Validator().Rules("required").Data(in.SignId).Messages("签名不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证通道
	//if err := g.Validator().Rules("required").Data(in.ChannelId).Messages("通道不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证模板
	//if err := g.Validator().Rules("required").Data(in.TemplateId).Messages("模板不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证短信内容
	if err := g.Validator().Rules("required").Data(in.Content).Messages("短信内容不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证号码数量
	//if err := g.Validator().Rules("required").Data(in.MobileNum).Messages("号码数量不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证发送状态
	//if err := g.Validator().Rules("required").Data(in.SendStatus).Messages("发送状态不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}
	//if err := g.Validator().Rules("in:1,2,3,4").Data(in.SendStatus).Messages("发送状态值不正确").Run(ctx); err != nil {
	//	return err.Current()
	//}

	// 验证审核状态
	//if err := g.Validator().Rules("required").Data(in.AuditStatus).Messages("审核状态不能为空").Run(ctx); err != nil {
	//	return err.Current()
	//}

	return
}

type DxSmsGroupSendEditModel struct{}

// DxSmsGroupSendDeleteInp 删除短信群发
type DxSmsGroupSendDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsGroupSendDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupSendDeleteModel struct{}

// DxSmsGroupSendViewInp 获取指定短信群发信息
type DxSmsGroupSendViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsGroupSendViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupSendViewModel struct {
	entity.DxSmsGroupSend
	DxSmsSignSignText         string            `json:"dxSmsSignSignText"         dc:"签名文本"`
	DxSmsGroupTemplateTplName string            `json:"dxSmsGroupTemplateTplName" dc:"模板名称"`
	MemberBySumma             *hook.MemberSumma `json:"memberBySumma" dc:"创建人摘要信息"`
	SysSmsChannelChannelName  string            `json:"sysSmsChannelChannelName" dc:"通道名称"`
}

// DxSmsGroupSendListInp 获取短信群发列表
type DxSmsGroupSendListInp struct {
	form.PageReq
	Id                        int64         `json:"id"                        dc:"ID"`
	Member                    string        `json:"member"                  dc:"用户"`
	BatchNo                   string        `json:"batchNo"                   dc:"批次号"`
	SmsMode                   int           `json:"smsMode"                   dc:"短信模式"`
	SendMode                  int           `json:"sendMode"                  dc:"发送模式"`
	SendStatus                int           `json:"sendStatus"                dc:"发送状态"`
	AuditStatus               int           `json:"auditStatus"               dc:"审核状态"`
	CreatedAt                 []*gtime.Time `json:"createdAt"                 dc:"创建时间"`
	DxSmsSignSignText         string        `json:"dxSmsSignSignText"         dc:"签名文本"`
	DxSmsGroupTemplateTplName string        `json:"dxSmsGroupTemplateTplName" dc:"模板名称"`
	AdminMemberRealName       string        `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername       string        `json:"adminMemberUsername" dc:"帐号"`
}

func (in *DxSmsGroupSendListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupSendListModel struct {
	Id                        int64             `json:"id"                        dc:"ID"`
	MemberId                  int64             `json:"memberId"                  dc:"用户ID"`
	BatchNo                   string            `json:"batchNo"                   dc:"批次号"`
	SmsMode                   int               `json:"smsMode"                   dc:"短信模式"`
	SendMode                  int               `json:"sendMode"                  dc:"发送模式"`
	SignId                    int64             `json:"signId"                    dc:"签名"`
	ChannelId                 int64             `json:"channelId"                 dc:"通道"`
	TemplateId                int64             `json:"templateId"                dc:"模板"`
	Content                   string            `json:"content"                   dc:"短信内容"`
	MobileNum                 int               `json:"mobileNum"                 dc:"号码数量"`
	Remark                    string            `json:"remark"                    dc:"备注"`
	SendStatus                int               `json:"sendStatus"                dc:"发送状态"`
	AuditStatus               int               `json:"auditStatus"               dc:"审核状态"`
	ScheduleTime              *gtime.Time       `json:"scheduleTime"              dc:"定时发送时间"`
	CycleStartDate            *gtime.Time       `json:"cycleStartDate"            dc:"周期发送开始日期"`
	CycleEndDate              *gtime.Time       `json:"cycleEndDate"              dc:"周期发送结束日期"`
	CycleStartTime            *gtime.Time       `json:"cycleStartTime"            dc:"周期发送开始时间"`
	CycleEndTime              *gtime.Time       `json:"cycleEndTime"              dc:"周期发送结束时间"`
	CycleFreq                 int               `json:"cycleFreq"                 dc:"周期发送频次/天"`
	CreatedAt                 *gtime.Time       `json:"createdAt"                 dc:"创建时间"`
	DxSmsSignSignText         string            `json:"dxSmsSignSignText"         dc:"签名文本"`
	DxSmsGroupTemplateTplName string            `json:"dxSmsGroupTemplateTplName" dc:"模板名称"`
	MemberBySumma             *hook.MemberSumma `json:"memberBySumma" dc:"创建人摘要信息"`
	AdminMemberRealName       string            `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername       string            `json:"adminMemberUsername" dc:"帐号"`
}

// DxSmsGroupSendCheckFileInp 检查文件输入参数
type DxSmsGroupSendCheckFileInp struct {
	AttachmentId string `json:"attachmentId" v:"required#附件ID不能为空" dc:"附件ID"`
	FileUrl      string `json:"fileUrl" v:"required#文件地址不能为空" dc:"文件地址"`
	TemplateId   int64  `json:"templateId" dc:"模板ID，大于0时为模板excel文件"`
}

// DxSmsGroupSendCheckFileModel 检查文件结果
type DxSmsGroupSendCheckFileModel struct {
	SuccessCount   int    `json:"successCount" dc:"成功条数"`
	FailCount      int    `json:"failCount"    dc:"失败条数"`
	DuplicateCount int    `json:"duplicateCount" dc:"重复条数"`
	NewFileUrl     string `json:"newFileUrl"   dc:"新的文件地址"`
}

// DxSmsGroupSendCancelInp 取消发送输入参数
type DxSmsGroupSendCancelInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsGroupSendCancelInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsGroupSendCancelModel struct{}
