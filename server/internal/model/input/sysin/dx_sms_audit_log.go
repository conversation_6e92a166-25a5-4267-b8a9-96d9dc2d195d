// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsAuditLogUpdateFields 修改系统_短信签名审核日志字段过滤
type DxSmsAuditLogUpdateFields struct {
	PkId        int64  `json:"pkId"      dc:"资源ID"`
	AuditFlag   int    `json:"auditFlag" dc:"审核标志"`
	AuditStatus int    `json:"auditStatus" dc:"审核结果"`
	AuditRemark string `json:"auditRemark" dc:"审核备注"`
}

// DxSmsAuditLogInsertFields 新增系统_短信签名审核日志字段过滤
type DxSmsAuditLogInsertFields struct {
	PkId        int64  `json:"pkId"      dc:"资源ID"`
	AuditFlag   int    `json:"auditFlag" dc:"审核标志"`
	AuditStatus int    `json:"auditStatus" dc:"审核结果"`
	AuditRemark string `json:"auditRemark" dc:"审核备注"`
	CreatedBy   int64  `json:"createdBy"   dc:"审核人"`
}

// DxSmsAuditLogEditInp 修改/新增系统_短信签名审核日志
type DxSmsAuditLogEditInp struct {
	entity.DxSmsAuditLog
	ChannelId int64 `json:"channelId" dc:"通道ID"`
}

func (in *DxSmsAuditLogEditInp) Filter(ctx context.Context) (err error) {
	// 验证资源ID
	if err := g.Validator().Rules("required").Data(in.PkId).Messages("资源ID不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	// 验证审核标志
	if err := g.Validator().Rules("required").Data(in.AuditFlag).Messages("审核标志不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	// 验证审核结果
	if err := g.Validator().Rules("required").Data(in.AuditStatus).Messages("审核结果不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:3,2,100,1").Data(in.AuditStatus).Messages("审核结果值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type DxSmsAuditLogEditModel struct{}

// DxSmsAuditLogViewInp 获取指定审核日志信息
type DxSmsAuditLogViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsAuditLogViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsAuditLogViewModel struct {
	entity.DxSmsAuditLog
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
}

// DxSmsAuditLogListInp 获取审核日志列表
type DxSmsAuditLogListInp struct {
	form.PageReq
	Id          int64         `json:"id"          dc:"ID"`
	PkId        int64         `json:"pkId"        dc:"资源ID"`
	AuditFlag   int           `json:"auditFlag"   dc:"审核标志"`
	AuditStatus int           `json:"auditStatus" dc:"审核状态"`
	CreatedBy   string        `json:"createdBy"   dc:"创建者"`
	CreatedAt   []*gtime.Time `json:"createdAt"   dc:"创建时间"`
}

func (in *DxSmsAuditLogListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsAuditLogListModel struct {
	Id             int64             `json:"id"             dc:"ID"`
	PkId           int64             `json:"pkId"           dc:"资源ID"`
	AuditFlag      int               `json:"auditFlag"      dc:"审核标志"`
	AuditStatus    int               `json:"auditStatus"    dc:"审核状态"`
	AuditRemark    string            `json:"auditRemark"    dc:"审核备注"`
	CreatedBy      int64             `json:"createdBy"      dc:"创建者"`
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	CreatedAt      *gtime.Time       `json:"createdAt"      dc:"创建时间"`
}

// DxSmsAuditLogExportModel 导出审核日志
type DxSmsAuditLogExportModel struct {
	Id          int64       `json:"id"          dc:"ID"`
	PkId        int64       `json:"pkId"        dc:"资源ID"`
	AuditFlag   int         `json:"auditFlag"   dc:"审核标志"`
	AuditStatus int         `json:"auditStatus" dc:"审核状态"`
	AuditRemark string      `json:"auditRemark" dc:"审核备注"`
	CreatedBy   int64       `json:"createdBy"   dc:"创建者"`
	CreatedAt   *gtime.Time `json:"createdAt"   dc:"创建时间"`
}

// DxSmsAuditLogViewByPkIdInp 获取指定审核日志信息
type DxSmsAuditLogViewByPkIdInp struct {
	PkId int64 `json:"pkId" v:"required#pkId不能为空" dc:"pkID"`
}

func (in *DxSmsAuditLogViewByPkIdInp) Filter(ctx context.Context) (err error) {
	return
}

// DxSmsAuditLogListByPkIdInp 获取审核日志信息
type DxSmsAuditLogListByPkIdInp struct {
	form.PageReq
	PkId      int64 `json:"pkId"      dc:"资源ID"`
	AuditFlag int   `json:"auditFlag"   dc:"审核标志"`
}

func (in *DxSmsAuditLogListByPkIdInp) Filter(ctx context.Context) (err error) {
	return
}
