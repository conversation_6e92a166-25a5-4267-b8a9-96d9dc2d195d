// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/consts"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	"hotgo/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSignUpdateFields 修改短信签名字段过滤
type DxSmsSignUpdateFields struct {
	SignText    string `json:"signText"    dc:"签名文本"`
	Remark      string `json:"remark"      dc:"备注"`
	Status      int    `json:"status"      dc:"状态"`
	AuditStatus int    `json:"auditStatus" dc:"审核状态"`
}

// DxSmsSignInsertFields 新增短信签名字段过滤
type DxSmsSignInsertFields struct {
	MemberId    int64  `json:"memberId"    dc:"用户ID"`
	SignText    string `json:"signText"    dc:"签名文本"`
	Remark      string `json:"remark"      dc:"备注"`
	Status      int    `json:"status"      dc:"状态"`
	AuditStatus int    `json:"auditStatus" dc:"审核状态"`
}

// DxSmsSignEditInp 修改/新增短信签名
type DxSmsSignEditInp struct {
	entity.DxSmsSign
}

func (in *DxSmsSignEditInp) Filter(ctx context.Context) (err error) {
	// 验证签名文本
	if err := g.Validator().Rules("required").Data(in.SignText).Messages("签名文本不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type DxSmsSignEditModel struct{}

// DxSmsSignDeleteInp 删除短信签名
type DxSmsSignDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsSignDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsSignDeleteModel struct{}

// DxSmsSignViewInp 获取指定短信签名信息
type DxSmsSignViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsSignViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsSignViewModel struct {
	entity.DxSmsSign
}

// DxSmsSignListInp 获取短信签名列表
type DxSmsSignListInp struct {
	form.PageReq
	Id                  int64         `json:"id"          dc:"ID"`
	SignText            string        `json:"signText"    dc:"签名文本"`
	Status              int           `json:"status"      dc:"状态"`
	AuditStatus         int           `json:"auditStatus" dc:"审核状态"`
	CreatedAt           []*gtime.Time `json:"createdAt"   dc:"创建时间"`
	AdminMemberRealName string        `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string        `json:"adminMemberUsername" dc:"帐号"`
}

func (in *DxSmsSignListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsSignListModel struct {
	Id                  int64             `json:"id"          dc:"ID"`
	MemberId            int64             `json:"memberId"    dc:"用户ID"`
	SignText            string            `json:"signText"    dc:"签名文本"`
	Remark              string            `json:"remark"      dc:"备注"`
	Status              int               `json:"status"      dc:"状态"`
	AuditStatus         int               `json:"auditStatus" dc:"审核状态"`
	MemberBySumma       *hook.MemberSumma `json:"memberBySumma" dc:"创建人摘要信息"`
	CreatedAt           *gtime.Time       `json:"createdAt"   dc:"创建时间"`
	UpdatedAt           *gtime.Time       `json:"updatedAt"   dc:"更新时间"`
	AdminMemberRealName string            `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string            `json:"adminMemberUsername" dc:"帐号"`
}

// DxSmsSignExportModel 导出短信签名
type DxSmsSignExportModel struct {
	Id                  int64       `json:"id"          dc:"ID"`
	AdminMemberRealName string      `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string      `json:"adminMemberUsername" dc:"子帐号"`
	SignText            string      `json:"signText"    dc:"签名文本"`
	Remark              string      `json:"remark"      dc:"备注"`
	Status              int         `json:"status"      dc:"状态"`
	AuditStatus         int         `json:"auditStatus" dc:"审核状态"`
	// MemberId            int64       `json:"memberId"    dc:"用户ID"`
	CreatedAt           *gtime.Time `json:"createdAt"   dc:"创建时间"`
	// UpdatedAt           *gtime.Time `json:"updatedAt"   dc:"更新时间"`
	
}

// DxSmsSignStatusInp 更新短信签名状态
type DxSmsSignStatusInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" dc:"状态"`
}

func (in *DxSmsSignStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type DxSmsSignStatusModel struct{}

// DxSmsSignOptionInp 短信签名选项
type DxSmsSignOptionInp struct{}

type DxSmsSignOption struct {
	Label string      `json:"label"     dc:"标签"`
	Value interface{} `json:"value"     dc:"键值"`
}
