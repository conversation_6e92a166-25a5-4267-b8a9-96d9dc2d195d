// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendRecordViewInp 获取指定短信记录信息
type DxSmsSendRecordViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxSmsSendRecordViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsSendRecordViewModel struct {
	entity.DxSmsSendRecord
	MemberBySumma       *hook.MemberSumma           `json:"memberBySumma" dc:"创建人摘要信息"`
	SmsChannelBySumma   *hook.SmsChannelSumma       `json:"smsChannelBySumma" dc:"短信通道"`
	AdminMemberRealName string                      `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string                      `json:"adminMemberUsername" dc:"帐号"`
	FailureLogs         []*DxSmsFailureLogListModel `json:"failureLogs" dc:"失败重发明细"`
}

// DxSmsSendRecordListInp 获取短信记录列表
type DxSmsSendRecordListInp struct {
	form.PageReq
	BatchNo                  string        `json:"batchNo"                  dc:"群发批次号"`
	Mobile                   string        `json:"mobile"                   dc:"发送号码"`
	CreatedResult            int           `json:"createdResult"            dc:"请求结果"`
	CreatedAt                []*gtime.Time `json:"createdAt"                dc:"请求时间"`
	SysSmsChannelChannelName string        `json:"sysSmsChannelChannelName" dc:"通道名称"`
	AdminMemberRealName      string        `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername      string        `json:"adminMemberUsername" dc:"帐号"`
	ChannelId                int64         `json:"channelId" dc:"通道ID"`
	DxSmsSignSignText        string        `json:"dxSmsSignSignText"         dc:"签名文本"`
}

func (in *DxSmsSendRecordListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsSendRecordListModel struct {
	Id                       int64             `json:"id"                       dc:"ID"`
	MemberId                 int64             `json:"memberId"   dc:"用户ID"`
	BatchNo                  string            `json:"batchNo"                  dc:"群发批次号"`
	Mobile                   string            `json:"mobile"                   dc:"发送号码"`
	SmsType                  int               `json:"smsType"                  dc:"短信类型"`
	SignText                 string            `json:"signText"                 dc:"短信签名"`
	Content                  string            `json:"content"                 dc:"发送内容"`
	FeeNum                   int               `json:"feeNum"                   dc:"计费条数"`
	UnitPrice                float64           `json:"unitPrice"                dc:"通道原始单价"`
	Source                   int               `json:"source"                   dc:"发送来源"`
	CreatedAt                *gtime.Time       `json:"createdAt"                dc:"请求时间"`
	CreatedResult            int               `json:"createdResult"            dc:"请求结果"`
	Ct                       *gtime.Time       `json:"ct"                       dc:"提交时间"`
	Result                   string            `json:"result"                   dc:"提交结果"`
	Sid                      string            `json:"sid"                      dc:"群发批次编号"`
	Seq                      string            `json:"seq"                      dc:"消息编号"`
	ReportStm                *gtime.Time       `json:"reportStm"                dc:"发送时间"`
	ReportSt                 *gtime.Time       `json:"reportSt"                 dc:"状态时间"`
	ReportSc                 string            `json:"reportSc"                 dc:"状态报告"`
	ReportChn                string            `json:"reportChn"                dc:"中文状态标识"`
	FailedReason             string            `json:"failedReason"      dc:"提交失败原因"`
	ReportTimeoutFlag        int               `json:"reportTimeoutFlag" dc:"状态报告超时标识（1：否，2：是）"`
	SysSmsChannelChannelName string            `json:"sysSmsChannelChannelName" dc:"通道名称"`
	MemberBySumma            *hook.MemberSumma `json:"memberBySumma" dc:"创建人摘要信息"`
	AdminMemberRealName      string            `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername      string            `json:"adminMemberUsername" dc:"帐号"`
}

// DxSmsSendRecordExportModel 导出短信记录
type DxSmsSendRecordExportModel struct {
	No                       int64       `json:"no"                       dc:"序号"`
	BatchNo                  string      `json:"batchNo"                  dc:"批次号"`
	AdminMemberRealName      string      `json:"adminMemberRealName" dc:"企业用户"`
	AdminMemberUsername      string      `json:"adminMemberUsername" dc:"子帐号"`
	Mobile                   string      `json:"mobile"                   dc:"发送号码"`
	SmsType                  int         `json:"smsType"                  dc:"短信类型"`
	SignText                 string      `json:"signText"                 dc:"短信签名"`
	Content                  string      `json:"content"                 dc:"发送内容"`
	FeeNum                   int         `json:"feeNum"                   dc:"计费条数"`
	UnitPrice                float64     `json:"unitPrice"                dc:"通道原始单价"`
	Source                   int         `json:"source"                   dc:"发送来源"`
	CreatedAt                *gtime.Time `json:"createdAt"                dc:"请求时间"`
	CreatedResult            string      `json:"createdResult"            dc:"请求结果"`
	Ct                       *gtime.Time `json:"ct"                       dc:"提交时间"`
	Result                   string      `json:"result"                   dc:"提交结果"`
	Sid                      string      `json:"sid"                      dc:"群发批次编号"`
	Seq                      string      `json:"seq"                      dc:"消息编号"`
	ReportStm                *gtime.Time `json:"reportStm"                dc:"发送时间"`
	ReportSt                 *gtime.Time `json:"reportSt"                 dc:"状态时间"`
	ReportSc                 string      `json:"reportSc"                 dc:"状态报告"`
	ReportChn                string      `json:"reportChn"                dc:"中文状态标识"`
	FailedReason             string      `json:"failedReason"      dc:"提交失败原因"`
	ReportTimeoutFlag        int         `json:"reportTimeoutFlag" dc:"状态报告超时标识（1：否，2：是）"`
	SysSmsChannelChannelName string      `json:"sysSmsChannelChannelName" dc:"通道名称"`
}

type DxSmsSendRecordExportByCompanyModel struct {
	No                  int64       `json:"no"                       dc:"序号"`
	BatchNo             string      `json:"batchNo"                  dc:"批次号"`
	AdminMemberUsername string      `json:"adminMemberUsername" dc:"发送账户"`
	Mobile              string      `json:"mobile"                   dc:"发送号码"`
	Content             string      `json:"content"                 dc:"发送内容"`
	CreatedAt           *gtime.Time `json:"createdAt"                dc:"提交时间"`
	CreatedResult       string      `json:"createdResult"            dc:"发送结果"`
	ReportSc            string      `json:"reportSc"                 dc:"状态报告"`
	FeeNum              int         `json:"feeNum"                   dc:"计费条数"`
}

type DxSmsSendRecordExportByCompanySubModel struct {
	No                  int64       `json:"no"                       dc:"序号"`
	AdminMemberUsername string      `json:"adminMemberUsername" dc:"发送账户"`
	BatchNo             string      `json:"batchNo"                  dc:"批次号"`
	Mobile              string      `json:"mobile"                   dc:"发送号码"`
	Content             string      `json:"content"                 dc:"发送内容"`
	CreatedAt           *gtime.Time `json:"createdAt"                dc:"提交时间"`
	CreatedResult       string      `json:"createdResult"            dc:"发送结果"`
	ReportSc            string      `json:"reportSc"                 dc:"状态报告"`
	FeeNum              int         `json:"feeNum"                   dc:"计费条数"`
}

// DxSmsSendRecordResendFailedInp 重发失败的短信记录
type DxSmsSendRecordResendFailedInp struct {
	BatchNo   string `json:"batchNo" v:"required#批次号不能为空" dc:"群发批次号"`
	ChannelId int64  `json:"channelId" dc:"通道ID，管理员可指定使用的通道"`
}

func (in *DxSmsSendRecordResendFailedInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsSendRecordResendFailedModel struct {
	Count   int    `json:"count" dc:"重新发送的数量"`
	BatchNo string `json:"batchNo" dc:"批次号"`
}

// DxSmsSendRecordStatTotalBillingInp 获取短信计费条数统计
type DxSmsSendRecordStatTotalBillingInp struct {
	DxSmsSendRecordListInp
}

// DxSmsSendRecordStatTotalBillingModel 短信计费条数统计
type DxSmsSendRecordStatTotalBillingModel struct {
	TotalBillingCount int64 `json:"totalBillingCount" dc:"总计费条数"`
}
