// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsChargeRecordUpdateFields 修改充值记录字段过滤
type DxSmsChargeRecordUpdateFields struct {
	MemberId   int64 `json:"memberId"   dc:"用户ID"`
	OpMemberId int64 `json:"opMemberId" dc:"操作人ID"`
	OpMode     int   `json:"opMode"     dc:"操作类型"`
	Num        int   `json:"num"        dc:"操作条数"`
}

// DxSmsChargeRecordInsertFields 新增充值记录字段过滤
type DxSmsChargeRecordInsertFields struct {
	MemberId   int64 `json:"memberId"   dc:"用户ID"`
	OpMemberId int64 `json:"opMemberId" dc:"操作人ID"`
	OpMode     int   `json:"opMode"     dc:"操作类型"`
	Num        int   `json:"num"        dc:"操作条数"`
}

// DxSmsChargeRecordEditInp 修改/新增充值记录
type DxSmsChargeRecordEditInp struct {
	entity.DxSmsChargeRecord
}

func (in *DxSmsChargeRecordEditInp) Filter(ctx context.Context) (err error) {

	return
}

type DxSmsChargeRecordEditModel struct{}

// DxSmsChargeRecordDeleteInp 删除充值记录
type DxSmsChargeRecordDeleteInp struct {
	Id interface{} `json:"id" v:"required# ID不能为空" dc:" ID"`
}

func (in *DxSmsChargeRecordDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsChargeRecordDeleteModel struct{}

// DxSmsChargeRecordViewInp 获取指定充值记录信息
type DxSmsChargeRecordViewInp struct {
	Id int64 `json:"id" v:"required# ID不能为空" dc:" ID"`
}

func (in *DxSmsChargeRecordViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsChargeRecordViewModel struct {
	entity.DxSmsChargeRecord
}

// DxSmsChargeRecordListInp 获取充值记录列表
type DxSmsChargeRecordListInp struct {
	form.PageReq
	Id                  int64         `json:"id"                  dc:"ID"`
	OpMode              int           `json:"opMode"              dc:"操作类型"`
	CreatedAt           []*gtime.Time `json:"createdAt"           dc:"操作时间"`
	AdminMemberRealName string        `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string        `json:"adminMemberUsername" dc:"帐号"`
}

func (in *DxSmsChargeRecordListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsChargeRecordListModel struct {
	Id                  int64               `json:"id"                  dc:"ID"`
	MemberId            int64               `json:"memberId"            dc:"用户ID"`
	OpMemberId          int64               `json:"opMemberId"          dc:"操作人ID"`
	OpMode              int                 `json:"opMode"              dc:"操作类型"`
	Num                 int                 `json:"num"                 dc:"操作条数"`
	CreatedAt           *gtime.Time         `json:"createdAt"           dc:"操作时间"`
	AdminMemberRealName string              `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string              `json:"adminMemberUsername" dc:"帐号"`
	OpMemberSumma       *hook.OpMemberSumma `json:"opMemberBySumma"       dc:"操作人摘要信息"`
}

// DxSmsChargeRecordExportModel 导出充值记录
type DxSmsChargeRecordExportModel struct {
	Id                  int64               `json:"id"                  dc:"ID"`
	MemberId            int64               `json:"memberId"            dc:"用户ID"`
	OpMemberId          int64               `json:"opMemberId"          dc:"操作人ID"`
	OpMode              int                 `json:"opMode"              dc:"操作类型"`
	Num                 int                 `json:"num"                 dc:"操作条数"`
	CreatedAt           *gtime.Time         `json:"createdAt"           dc:"操作时间"`
	AdminMemberRealName string              `json:"adminMemberRealName" dc:"真实姓名"`
	AdminMemberUsername string              `json:"adminMemberUsername" dc:"帐号"`
	OpMemberSumma       *hook.OpMemberSumma `json:"opMemberBySumma"       dc:"操作人摘要信息"`
}
