// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"fmt"
	"hotgo/internal/consts"
	"hotgo/internal/library/contexts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CompanySubAccountUpdateFields 修改企业子账户字段过滤
type CompanySubAccountUpdateFields struct {
	Pid                int64       `json:"pid"                dc:"父ID"`
	DeptId             int64       `json:"deptId"             dc:"部门ID"`
	MemberId           int64       `json:"memberId"           dc:"用户ID"`
	CompanyName        string      `json:"companyName"        dc:"名称"`
	CompanyAccount     string      `json:"companyAccount"     dc:"账号"`
	Leader             string      `json:"leader"             dc:"负责人"`
	Phone              string      `json:"phone"              dc:"联系电话"`
	Email              string      `json:"email"              dc:"邮箱"`
	Address            string      `json:"address"            dc:"地址"`
	Attachfiles        *gjson.Json `json:"attachfiles"        dc:"附件列表"`
	ChannelId          int64       `json:"channelId"          dc:"短信通道"`
	SmsType            int         `json:"smsType"            dc:"短信类型"`
	GroupSendAuditMode int         `json:"groupSendAuditMode" dc:"群发鉴权模式"`
	TemplateAuditMode  int         `json:"templateAuditMode"  dc:"模板鉴权模式"`
	SecretKey          string      `json:"secretKey"          dc:"企业密钥"`
	Level              int         `json:"level"              dc:"层级"`
	Tree               string      `json:"tree"               dc:"树形结构"`
	Sort               int         `json:"sort"               dc:"排序"`
}

// CompanySubAccountInsertFields 新增企业子账户字段过滤
type CompanySubAccountInsertFields struct {
	Pid                int64       `json:"pid"                dc:"父ID"`
	DeptId             int64       `json:"deptId"             dc:"部门ID"`
	MemberId           int64       `json:"memberId"           dc:"用户ID"`
	CompanyName        string      `json:"companyName"        dc:"名称"`
	CompanyAccount     string      `json:"companyAccount"     dc:"账号"`
	Leader             string      `json:"leader"             dc:"负责人"`
	Phone              string      `json:"phone"              dc:"联系电话"`
	Email              string      `json:"email"              dc:"邮箱"`
	Address            string      `json:"address"            dc:"地址"`
	Attachfiles        *gjson.Json `json:"attachfiles"        dc:"附件列表"`
	ChannelId          int64       `json:"channelId"          dc:"短信通道"`
	SmsType            int         `json:"smsType"            dc:"短信类型"`
	GroupSendAuditMode int         `json:"groupSendAuditMode" dc:"群发鉴权模式"`
	TemplateAuditMode  int         `json:"templateAuditMode"  dc:"模板鉴权模式"`
	SecretKey          string      `json:"secretKey"          dc:"企业密钥"`
	Level              int         `json:"level"              dc:"层级"`
	Tree               string      `json:"tree"               dc:"树形结构"`
	Sort               int         `json:"sort"               dc:"排序"`
}

// CompanySubAccountEditInp 修改/新增企业子账户
type CompanySubAccountEditInp struct {
	entity.AdminCompany
	UserName             string                `json:"userName" dc:"企业账号"`
	Password             string                `json:"password" dc:"账号密码"`
	ResendChannels       []int64               `json:"resendChannels" dc:"补发通道列表"`       // 兼容原有简单格式
	ResendChannelConfigs []ResendChannelConfig `json:"resendChannelConfigs" dc:"重发通道配置"` // 新的带优先级格式
}

func (in *CompanySubAccountEditInp) Filter(ctx context.Context) (err error) {
	// 验证名称
	if err := g.Validator().Rules("required").Data(in.CompanyName).Messages("名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	// 验证账号
	if err := g.Validator().Rules("required").Data(in.UserName).Messages("账号不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	// 验证密码
	if in.Id == 0 {
		if err := g.Validator().Rules("required").Data(in.Password).Messages("密码不能为空").Run(ctx); err != nil {
			return err.Current()
		}
	}

	// 验证通道
	if in.Pid > 0 {
		if err := g.Validator().Rules("min:1").Data(in.ChannelId).Messages("请选择短信通道").Run(ctx); err != nil {
			return err.Current()
		}
	}

	return
}

type CompanySubAccountEditModel struct{}

// CompanySubAccountDeleteInp 删除企业子账户
type CompanySubAccountDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *CompanySubAccountDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanySubAccountDeleteModel struct{}

// CompanySubAccountViewInp 获取指定企业子账户信息
type CompanySubAccountViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *CompanySubAccountViewInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanySubAccountViewModel struct {
	entity.AdminCompany
	UserName           string              `json:"userName"`
	SmsChannelName     string              `json:"smsChannelName" orm:"channel_name"`
	ResendChannels     []int64             `json:"resendChannels" dc:"补发通道列表"`
	ResendChannelInfos []ResendChannelInfo `json:"resendChannelInfos" dc:"重发通道详细信息"`
}

// CompanySubAccountListInp 获取企业子账户列表
type CompanySubAccountListInp struct {
	form.PageReq
	CompanyName             string      `json:"companyName"             dc:"名称"`
	CompanyAccount          string      `json:"companyAccount"          dc:"账号"`
	AdminMemberStatus       int         `json:"adminMemberStatus"       dc:"状态"`
	AdminMemberCreatedAt    *gtime.Time `json:"adminMemberCreatedAt"    dc:"创建时间"`
	AdminCompanyCompanyName string      `json:"adminCompanyCompanyName" dc:"所属企业"`
}

func (in *CompanySubAccountListInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanySubAccountListModel struct {
	Id                      int64       `json:"id"                      dc:"ID"`
	CompanyName             string      `json:"companyName"             dc:"名称"`
	CompanyAccount          string      `json:"companyAccount"          dc:"账号"`
	Balance                 float64     `json:"balance"                 dc:"余额"`
	SmsBalance              int         `json:"smsBalance"              dc:"短信余额"`
	SmsFrozen               int         `json:"smsFrozen"               dc:"短信冻结"`
	Leader                  string      `json:"leader"                  dc:"负责人"`
	Phone                   string      `json:"phone"                   dc:"联系电话"`
	Email                   string      `json:"email"                   dc:"邮箱"`
	Address                 string      `json:"address"                 dc:"地址"`
	ChannelId               int64       `json:"channelId"               dc:"短信通道"`
	SmsType                 int         `json:"smsType"                 dc:"短信类型"`
	GroupSendAuditMode      int         `json:"groupSendAuditMode"      dc:"群发鉴权模式"`
	TemplateAuditMode       int         `json:"templateAuditMode"       dc:"模板鉴权模式"`
	SmsAmount               int         `json:"smsAmount"               dc:"短信总额"`
	AdminMemberStatus       int         `json:"adminMemberStatus"       dc:"状态"`
	AdminMemberCreatedAt    *gtime.Time `json:"adminMemberCreatedAt"    dc:"创建时间"`
	AdminCompanyCompanyName string      `json:"adminCompanyCompanyName" dc:"所属企业"`
}

// CompanySubAccountExportModel 导出企业子账户
type CompanySubAccountExportModel struct {
	Id                 int64   `json:"id"                      dc:"ID"`
	CompanyName        string  `json:"companyName"             dc:"名称"`
	CompanyAccount     string  `json:"companyAccount"          dc:"账号"`
	Balance            float64 `json:"balance"                 dc:"余额"`
	SmsBalance         int     `json:"smsBalance"              dc:"短信余额"`
	Leader             string  `json:"leader"                  dc:"负责人"`
	Phone              string  `json:"phone"                   dc:"联系电话"`
	Email              string  `json:"email"                   dc:"邮箱"`
	Address            string  `json:"address"                 dc:"地址"`
	ChannelId          int64   `json:"channelId"               dc:"短信通道"`
	SmsType            int     `json:"smsType"                 dc:"短信类型"`
	GroupSendAuditMode int     `json:"groupSendAuditMode"      dc:"群发鉴权模式"`
	TemplateAuditMode  int     `json:"templateAuditMode"       dc:"模板鉴权模式"`
	SmsAmount          int     `json:"smsAmount"               dc:"短信总额"`
}

// CompanySubAccountMaxSortInp 获取企业子账户最大排序
type CompanySubAccountMaxSortInp struct{}

func (in *CompanySubAccountMaxSortInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanySubAccountMaxSortModel struct {
	Sort int `json:"sort"  description:"排序"`
}

// CompanySubAccountStatusInp 更新企业子账户状态
type CompanySubAccountStatusInp struct {
	Id                int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	AdminMemberStatus int   `json:"adminMemberStatus" v:"required|in:1,2#状态不能为空|状态值不正确" dc:"状态"`
}

func (in *CompanySubAccountStatusInp) Filter(ctx context.Context) (err error) {
	return
}

type CompanySubAccountStatusModel struct{}

// CompanySubAccountAddBalanceInp 变更企业子账户余额
type CompanySubAccountAddBalanceInp struct {
	Id               int64   `json:"id"          v:"required#用户ID不能为空"         dc:"管理员ID"`
	OperateMode      int64   `json:"operateMode"      v:"in:1,2#输入的操作方式是无效的"     dc:"操作方式"`
	Num              float64 `json:"num"                dc:"操作数量"`
	AppId            string  `json:"appId"`
	AddonsName       string  `json:"addonsName"`
	SelfNum          float64 `json:"selfNum"`
	SelfCreditGroup  string  `json:"selfCreditGroup"`
	OtherNum         float64 `json:"otherNum"`
	OtherCreditGroup string  `json:"otherCreditGroup"`
	Remark           string  `json:"remark"`
}

func (in *CompanySubAccountAddBalanceInp) Filter(ctx context.Context) (err error) {
	if in.Num <= 0 {
		err = gerror.New("操作数量必须大于0")
		return
	}

	if in.OperateMode == 1 {
		// 加款
		in.SelfNum = -in.Num
		in.SelfCreditGroup = consts.CreditGroupOpIncr
		in.OtherNum = in.Num
		in.OtherCreditGroup = consts.CreditGroupIncr
		in.Remark = fmt.Sprintf("增加余额:%v", in.OtherNum)
	} else {
		// 扣款
		in.SelfNum = in.Num
		in.SelfCreditGroup = consts.CreditGroupOpDecr
		in.OtherNum = -in.Num
		in.OtherCreditGroup = consts.CreditGroupDecr
		in.Remark = fmt.Sprintf("扣除余额:%v", in.OtherNum)
	}

	in.AppId = contexts.GetModule(ctx)
	in.AddonsName = contexts.GetAddonName(ctx)
	return
}

type CompanySubAccountAddBalanceModel struct{}

// CompanySubAccountAddSmsBalanceInp 企业子账户短信充值
type CompanySubAccountAddSmsBalanceInp struct {
	Id               int64  `json:"id"          v:"required#用户ID不能为空"         dc:"管理员ID"`
	OperateMode      int64  `json:"operateMode"      v:"in:1,2#输入的操作方式是无效的"     dc:"操作方式"`
	Num              int    `json:"num"                dc:"操作数量"`
	AppId            string `json:"appId"`
	AddonsName       string `json:"addonsName"`
	SelfNum          int    `json:"selfNum"`
	SelfCreditGroup  string `json:"selfCreditGroup"`
	OtherNum         int    `json:"otherNum"`
	OtherCreditGroup string `json:"otherCreditGroup"`
	Remark           string `json:"remark"`
}

func (in *CompanySubAccountAddSmsBalanceInp) Filter(ctx context.Context) (err error) {
	if in.Num <= 0 {
		err = gerror.New("操作数量必须大于0")
		return
	}

	if in.OperateMode == 1 {
		// 加款
		in.SelfNum = -in.Num
		in.SelfCreditGroup = consts.CreditGroupOpIncr
		in.OtherNum = in.Num
		in.OtherCreditGroup = consts.CreditGroupIncr
		in.Remark = fmt.Sprintf("增加短信:%v", in.OtherNum)
	} else {
		// 扣款
		in.SelfNum = in.Num
		in.SelfCreditGroup = consts.CreditGroupOpDecr
		in.OtherNum = -in.Num
		in.OtherCreditGroup = consts.CreditGroupDecr
		in.Remark = fmt.Sprintf("扣除短信:%v", in.OtherNum)
	}

	in.AppId = contexts.GetModule(ctx)
	in.AddonsName = contexts.GetAddonName(ctx)
	return
}

type CompanySubAccountAddSmsBalanceModel struct{}
