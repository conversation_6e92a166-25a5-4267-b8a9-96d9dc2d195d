// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DxBusinessReportingUpdateFields 修改商机报备字段过滤
type DxBusinessReportingUpdateFields struct {
	BusinessName        string      `json:"businessName"        dc:"业务名称"`
	BusinessArea        int64       `json:"businessArea"        dc:"业务区域"`
	CustomerManager     string      `json:"customerManager"     dc:"客户经理"`
	LinkTel             string      `json:"linkTel"             dc:"联系电话"`
	CustomerOrg         string      `json:"customerOrg"         dc:"客户单位"`
	BusinessRequirement *gjson.Json `json:"businessRequirement" dc:"业务需求"`
	ServiceNumber       string      `json:"serviceNumber"       dc:"接入号"`
	Attachments         *gjson.Json `json:"attachments"         dc:"附件"`
	Remark              string      `json:"remark"              dc:"备注"`
	CreatedTime         *gtime.Time `json:"createdTime"         dc:"创建时间"`
	BudgetAmount        float64     `json:"budgetAmount"        dc:"预算金额"`
	// InvoiceAmount       float64     `json:"invoiceAmount"       dc:"开票金额"`
	// SettlementAmount    float64     `json:"settlementAmount"    dc:"结算净额"`
	// ExpenditureAmount   float64     `json:"expenditureAmount"   dc:"支出金额"`
	// RetainedAmount      float64     `json:"retainedAmount"      dc:"留存金额"`
	// RetentionRatio      float64     `json:"retentionRatio"      dc:"留存比率"`
	// Status              int         `json:"status"              dc:"状态"`
	// InvoiceAttachments  *gjson.Json `json:"invoiceAttachments"  dc:"开票附件"`
	// PaymentAttachments  *gjson.Json `json:"paymentAttachments"  dc:"回款附件"`
	// FinishAttachments   *gjson.Json `json:"finishAttachments"   dc:"完成附件"`
}

// DxBusinessReportingInsertFields 新增商机报备字段过滤
type DxBusinessReportingInsertFields struct {
	BusinessName        string      `json:"businessName"        dc:"业务名称"`
	BusinessArea        int64       `json:"businessArea"        dc:"业务区域"`
	CustomerManager     string      `json:"customerManager"     dc:"客户经理"`
	LinkTel             string      `json:"linkTel"             dc:"联系电话"`
	CustomerOrg         string      `json:"customerOrg"         dc:"客户单位"`
	BusinessRequirement *gjson.Json `json:"businessRequirement" dc:"业务需求"`
	ServiceNumber       string      `json:"serviceNumber"       dc:"接入号"`
	Attachments         *gjson.Json `json:"attachments"         dc:"附件"`
	Remark              string      `json:"remark"              dc:"备注"`
	CreatedTime         *gtime.Time `json:"createdTime"         dc:"创建时间"`
	BudgetAmount        float64     `json:"budgetAmount"        dc:"预算金额"`
	// InvoiceAmount       float64     `json:"invoiceAmount"       dc:"开票金额"`
	// SettlementAmount    float64     `json:"settlementAmount"    dc:"结算净额"`
	// ExpenditureAmount   float64     `json:"expenditureAmount"   dc:"支出金额"`
	// RetainedAmount      float64     `json:"retainedAmount"      dc:"留存金额"`
	// RetentionRatio      float64     `json:"retentionRatio"      dc:"留存比率"`
	// Status              int         `json:"status"              dc:"状态"`
	// InvoiceAttachments  *gjson.Json `json:"invoiceAttachments"  dc:"开票附件"`
	// PaymentAttachments  *gjson.Json `json:"paymentAttachments"  dc:"回款附件"`
	// FinishAttachments   *gjson.Json `json:"finishAttachments"   dc:"完成附件"`
}

// DxBusinessReportingEditInp 修改/新增商机报备
type DxBusinessReportingEditInp struct {
	entity.DxBusinessReporting
}

func (in *DxBusinessReportingEditInp) Filter(ctx context.Context) (err error) {
	// 验证业务名称
	if err := g.Validator().Rules("required").Data(in.BusinessName).Messages("业务名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证业务区域
	if err := g.Validator().Rules("required").Data(in.BusinessArea).Messages("业务区域不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证客户经理
	if err := g.Validator().Rules("required").Data(in.CustomerManager).Messages("客户经理不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证联系电话
	/* if err := g.Validator().Rules("required").Data(in.LinkTel).Messages("联系电话不能为空").Run(ctx); err != nil {
		return err.Current()
	} */

	// 验证客户单位
	if err := g.Validator().Rules("required").Data(in.CustomerOrg).Messages("客户单位不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证接入号
	// if err := g.Validator().Rules("required").Data(in.ServiceNumber).Messages("接入号不能为空").Run(ctx); err != nil {
	// 	return err.Current()
	// }

	// 验证备注
	// if err := g.Validator().Rules("required").Data(in.Remark).Messages("备注不能为空").Run(ctx); err != nil {
	// 	return err.Current()
	// }

	// 验证预算金额
	/* if err := g.Validator().Rules("required").Data(in.BudgetAmount).Messages("预算金额不能为空").Run(ctx); err != nil {
		return err.Current()
	} */

	// 验证开票金额
	/* if err := g.Validator().Rules("required").Data(in.InvoiceAmount).Messages("开票金额不能为空").Run(ctx); err != nil {
		return err.Current()
	} */

	// 验证结算净额
	/* if err := g.Validator().Rules("required").Data(in.SettlementAmount).Messages("结算净额不能为空").Run(ctx); err != nil {
		return err.Current()
	} */

	// 验证支出金额
	/* if err := g.Validator().Rules("required").Data(in.ExpenditureAmount).Messages("支出金额不能为空").Run(ctx); err != nil {
		return err.Current()
	} */

	// 验证留存金额
	/* if err := g.Validator().Rules("required").Data(in.RetainedAmount).Messages("留存金额不能为空").Run(ctx); err != nil {
		return err.Current()
	} */

	// 验证留存比率
	/* if err := g.Validator().Rules("required").Data(in.RetentionRatio).Messages("留存比率不能为空").Run(ctx); err != nil {
		return err.Current()
	} */

	// 验证状态
	/* if err := g.Validator().Rules("required").Data(in.Status).Messages("状态不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2,3,4,5").Data(in.Status).Messages("状态值不正确").Run(ctx); err != nil {
		return err.Current()
	} */

	return
}

type DxBusinessReportingEditModel struct{}

// DxBusinessReportingDeleteInp 删除商机报备
type DxBusinessReportingDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxBusinessReportingDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type DxBusinessReportingDeleteModel struct{}

// DxBusinessReportingViewInp 获取指定商机报备信息
type DxBusinessReportingViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DxBusinessReportingViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DxBusinessReportingViewModel struct {
	entity.DxBusinessReporting
	Area               string  `json:"area"              dc:"业务区域标签"`
	InvoiceByUsername  string  `json:"invoiceByUsername" dc:"开票操作人"`
	PaymentByUsername  string  `json:"paymentByUsername" dc:"回款操作人"`
	FinishByUsername   string  `json:"finishByUsername" dc:"完成操作人"`
	DeliveryByUsername string  `json:"deliveryByUsername" dc:"交付操作人"`
	CompanyName        string  `json:"companyName"       dc:"企业名称"`
	CompanyAccount     string  `json:"companyAccount"    dc:"企业账号"`
	SmsBalance         float64 `json:"smsBalance"       dc:"短信余额"`
	SmsAmount          float64 `json:"smsAmount"        dc:"短信总额"`
}

// DxBusinessReportingListInp 获取商机报备列表
type DxBusinessReportingListInp struct {
	form.PageReq
	Id              int64         `json:"id"     dc:"ID"`
	Status          int           `json:"status" dc:"状态"`
	CreatedTime     []*gtime.Time `json:"createdTime" dc:"创建时间"`
	BusinessArea    int64         `json:"businessArea"        orm:"business_area"        description:"业务区域"`
	CustomerManager string        `json:"customerManager"   dc:"客户经理"`
	CustomerOrg     string        `json:"customerOrg"       dc:"客户单位"`
	ServiceNumber   string        `json:"serviceNumber"     dc:"接入号"`
}

func (in *DxBusinessReportingListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxBusinessReportingListModel struct {
	Id                        int64       `json:"id"                dc:"ID"`
	BusinessName              string      `json:"businessName"      dc:"业务名称"`
	BusinessArea              int64       `json:"businessArea"      dc:"业务区域"`
	Area                      string      `json:"area"              dc:"业务区域标签"`
	CustomerManager           string      `json:"customerManager"   dc:"客户经理"`
	LinkTel                   string      `json:"linkTel"           dc:"联系电话"`
	CustomerOrg               string      `json:"customerOrg"       dc:"客户单位"`
	ServiceNumber             string      `json:"serviceNumber"     dc:"接入号"`
	BusinessRequirement       *gjson.Json `json:"businessRequirement" dc:"业务需求"`
	BusinessRequirements      string      `json:"businessRequirements" dc:"业务需求"`
	BusinessRequirementsLabel string      `json:"businessRequirementsLabel" dc:"业务需求标签"`
	Remark                    string      `json:"remark"            dc:"备注"`
	BudgetAmount              float64     `json:"budgetAmount"      dc:"预算金额"`
	InvoiceAmount             float64     `json:"invoiceAmount"     dc:"开票金额"`
	SettlementAmount          float64     `json:"settlementAmount"  dc:"结算净额"`
	ExpenditureAmount         float64     `json:"expenditureAmount" dc:"支出金额"`
	RetainedAmount            float64     `json:"retainedAmount"    dc:"留存金额"`
	RetentionRatio            float64     `json:"retentionRatio"    dc:"留存比率"`
	CreatedTime               *gtime.Time `json:"createdTime"       dc:"创建时间"`
	Status                    int         `json:"status"            dc:"状态"`
	DeliveryAmount            float64     `json:"deliveryAmount"    dc:"交付金额"`
	CompanyName               string      `json:"companyName"       dc:"企业名称"`
	CompanyAccount            string      `json:"companyAccount"    dc:"企业账号"`
	SmsBalance                float64     `json:"smsBalance"        dc:"短信余额"`
	SmsAmount                 float64     `json:"smsAmount"         dc:"短信总额"`
}

// DxBusinessReportingExportModel 导出商机报备
type DxBusinessReportingExportModel struct {
	No                   int64       `json:"no"                dc:"序号"`
	BusinessName         string      `json:"businessName"      dc:"业务名称"`
	Area                 string      `json:"area"              dc:"业务区域"`
	CustomerManager      string      `json:"customerManager"   dc:"客户经理"`
	LinkTel              string      `json:"linkTel"           dc:"联系电话"`
	CustomerOrg          string      `json:"customerOrg"       dc:"客户单位"`
	ServiceNumber        string      `json:"serviceNumber"     dc:"接入号"`
	BusinessRequirements string      `json:"businessRequirementsLabel" dc:"业务需求"`
	Remark               string      `json:"remark"            dc:"备注"`
	BudgetAmount         float64     `json:"budgetAmount"      dc:"预算金额"`
	InvoiceAmount        float64     `json:"invoiceAmount"     dc:"开票金额"`
	SettlementAmount     float64     `json:"settlementAmount"  dc:"结算净额"`
	DeliveryAmount       float64     `json:"deliveryAmount"    dc:"交付成本"`
	ExpenditureAmount    float64     `json:"expenditureAmount" dc:"支出金额"`
	RetainedAmount       float64     `json:"retainedAmount"    dc:"留存金额"`
	RetentionRatio       float64     `json:"retentionRatio"    dc:"留存比率"`
	CreatedTime          *gtime.Time `json:"createdTime"       dc:"创建时间"`
	StatusText           string      `json:"statusText"        dc:"状态"`
	CompanyName          string      `json:"companyName"       dc:"企业名称"`
	CompanyAccount       string      `json:"companyAccount"    dc:"企业账号"`
	SmsBalance           float64     `json:"smsBalance"        dc:"短信余额"`
	SmsAmount            float64     `json:"smsAmount"         dc:"短信总额"`
}

// DxBusinessReportingStatusInp 更新商机报备状态
type DxBusinessReportingStatusInp struct {
	Id                 int64       `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status             int         `json:"status" dc:"状态"`
	InvoiceAmount      float64     `json:"invoiceAmount" dc:"开票金额"`
	SettlementAmount   float64     `json:"settlementAmount" dc:"结算净额"`
	InvoiceAttachments *gjson.Json `json:"invoiceAttachments"  dc:"开票附件"`
	InvoiceDatetime    *gtime.Time `json:"invoiceDatetime"     dc:"开票时间"`
	InvoiceRemark      string      `json:"invoiceRemark"       dc:"开票备注"`
	PaymentAttachments *gjson.Json `json:"paymentAttachments"  dc:"回款附件"`
	PaymentDatetime    *gtime.Time `json:"paymentDatetime"     dc:"回款时间"`
	PaymentRemark      string      `json:"paymentRemark"       dc:"回款备注"`
	ExpenditureAmount  float64     `json:"expenditureAmount"   dc:"支出金额"`
	FinishAttachments  *gjson.Json `json:"finishAttachments"   dc:"完成附件"`
	FinishDatetime     *gtime.Time `json:"finishDatetime"      dc:"完成时间"`
	FinishRemark       string      `json:"finishRemark"        dc:"完成备注"`
	RetainedAmount     float64     `json:"retainedAmount"      dc:"留存金额"`
	RetentionRatio     float64     `json:"retentionRatio"      dc:"留存比率"`
}

func (in *DxBusinessReportingStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	// if !validate.InSlice(consts.StatusSlice, in.Status) {
	// 	err = gerror.New("状态不正确")
	// 	return
	// }
	return
}

type DxBusinessReportingStatusModel struct{}

// DxBusinessReportingStatTotalAmountInp 获取总金额统计
type DxBusinessReportingStatTotalAmountInp struct {
	DxBusinessReportingListInp
}

// DxBusinessReportingStatTotalAmountModel 总金额统计返回结构
type DxBusinessReportingStatTotalAmountModel struct {
	TotalBudgetAmount      float64 `json:"totalBudgetAmount"       dc:"预算总金额"`
	TotalInvoiceAmount     float64 `json:"totalInvoiceAmount"      dc:"开票总金额"`
	TotalPaymentAmount     float64 `json:"totalPaymentAmount"      dc:"回款总金额"`
	TotalSettlementAmount  float64 `json:"totalSettlementAmount"   dc:"结算净额总金额"`
	TotalDeliveryAmount    float64 `json:"totalDeliveryAmount"     dc:"交付成本总金额"`
	TotalExpenditureAmount float64 `json:"totalExpenditureAmount"  dc:"支出总金额"`
	TotalRetainedAmount    float64 `json:"totalRetainedAmount"     dc:"留存总金额"`
	TotalRetentionRatio    float64 `json:"totalRetentionRatio"     dc:"总体留存比率"`
}

// DxBusinessReportingExportReconciliationModel 导出对账单
type DxBusinessReportingExportReconciliationModel struct {
	No                   int64   `json:"no"                dc:"序号"`
	BusinessName         string  `json:"businessName"      dc:"业务名称"`
	Area                 string  `json:"area"              dc:"业务区域"`
	CustomerManager      string  `json:"customerManager"   dc:"客户经理"`
	LinkTel              string  `json:"linkTel"           dc:"联系电话"`
	CustomerOrg          string  `json:"customerOrg"       dc:"客户单位"`
	ServiceNumber        string  `json:"serviceNumber"     dc:"接入号"`
	BusinessRequirements string  `json:"businessRequirementsLabel" dc:"业务需求"`
	BudgetAmount         float64 `json:"budgetAmount"      dc:"预算金额"`
}

// DxBusinessReportingDeliveryInp 商机报备交付操作
type DxBusinessReportingDeliveryInp struct {
	Id                  int64       `json:"id" v:"required#ID不能为空" dc:"ID"`
	DeliveryAmount      float64     `json:"deliveryAmount" v:"required#交付金额不能为空" dc:"交付金额"`
	DeliveryDatetime    *gtime.Time `json:"deliveryDatetime" v:"required#交付时间不能为空" dc:"交付时间"`
	DeliveryAttachments *gjson.Json `json:"deliveryAttachments" v:"required#交付附件不能为空" dc:"交付附件"`
	DeliveryRemark      string      `json:"deliveryRemark" dc:"交付备注"`
}

func (in *DxBusinessReportingDeliveryInp) Filter(ctx context.Context) (err error) {
	return
}

type DxBusinessReportingDeliveryModel struct{}

// DxBusinessReportingAssociateMemberInp 关联客户
type DxBusinessReportingAssociateMemberInp struct {
	Id       int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	MemberId int64 `json:"member_id" v:"required#客户ID不能为空" dc:"客户ID"`
}

func (in *DxBusinessReportingAssociateMemberInp) Filter(ctx context.Context) (err error) {
	return
}

type DxBusinessReportingAssociateMemberModel struct{}

// DxBusinessReportingStatusRollbackInp 商机报备状态回退
type DxBusinessReportingStatusRollbackInp struct {
	Id     int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
	Status int   `json:"status" v:"required|min:1|max:3#状态不能为空#状态不能小于1#状态不能大于3" dc:"状态"`
}

func (in *DxBusinessReportingStatusRollbackInp) Filter(ctx context.Context) (err error) {
	return
}

type DxBusinessReportingStatusRollbackModel struct{}
