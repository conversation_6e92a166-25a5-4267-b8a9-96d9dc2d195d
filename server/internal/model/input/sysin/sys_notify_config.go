// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/consts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
	"hotgo/utility/validate"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysNotifyConfigUpdateFields 修改系统_审核通知配置字段过滤
type SysNotifyConfigUpdateFields struct {
	Category int    `json:"category" dc:"通知分类"`
	Name     string `json:"name"     dc:"通知名称"`
	SignText string `json:"signText" dc:"短信签名"`
	Content  string `json:"content"  dc:"短信内容"`
	Mobiles  string `json:"mobiles"  dc:"通知号码"`
	MemberId int64  `json:"memberId" dc:"关联用户ID"`
	Status   int    `json:"status"   dc:"状态"`
}

// SysNotifyConfigInsertFields 新增系统_审核通知配置字段过滤
type SysNotifyConfigInsertFields struct {
	Category int    `json:"category" dc:"通知分类"`
	Name     string `json:"name"     dc:"通知名称"`
	SignText string `json:"signText" dc:"短信签名"`
	Content  string `json:"content"  dc:"短信内容"`
	Mobiles  string `json:"mobiles"  dc:"通知号码"`
	MemberId int64  `json:"memberId" dc:"关联用户ID"`
	Status   int    `json:"status"   dc:"状态"`
}

// SysNotifyConfigEditInp 修改/新增系统_审核通知配置
type SysNotifyConfigEditInp struct {
	entity.SysNotifyConfig
}

func (in *SysNotifyConfigEditInp) Filter(ctx context.Context) (err error) {
	// 验证通知分类
	if err := g.Validator().Rules("required").Data(in.Category).Messages("通知分类不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2,3").Data(in.Category).Messages("通知分类值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证通知名称
	if err := g.Validator().Rules("required").Data(in.Name).Messages("通知名称不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证短信签名
	if err := g.Validator().Rules("required").Data(in.SignText).Messages("短信签名不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证短信内容
	if err := g.Validator().Rules("required").Data(in.Content).Messages("短信内容不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证通知号码
	if err := g.Validator().Rules("required").Data(in.Mobiles).Messages("通知号码不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证关联用户ID
	if err := g.Validator().Rules("required").Data(in.MemberId).Messages("关联用户ID不能为空").Run(ctx); err != nil {
		return err.Current()
	}

	// 验证状态
	if err := g.Validator().Rules("required").Data(in.Status).Messages("状态不能为空").Run(ctx); err != nil {
		return err.Current()
	}
	if err := g.Validator().Rules("in:1,2").Data(in.Status).Messages("状态值不正确").Run(ctx); err != nil {
		return err.Current()
	}

	return
}

type SysNotifyConfigEditModel struct{}

// SysNotifyConfigDeleteInp 删除系统_审核通知配置
type SysNotifyConfigDeleteInp struct {
	Id interface{} `json:"id" v:"required#主键ID不能为空" dc:"主键ID"`
}

func (in *SysNotifyConfigDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type SysNotifyConfigDeleteModel struct{}

// SysNotifyConfigViewInp 获取指定系统_审核通知配置信息
type SysNotifyConfigViewInp struct {
	Id int64 `json:"id" v:"required#主键ID不能为空" dc:"主键ID"`
}

func (in *SysNotifyConfigViewInp) Filter(ctx context.Context) (err error) {
	return
}

type SysNotifyConfigViewModel struct {
	entity.SysNotifyConfig
}

// SysNotifyConfigListInp 获取系统_审核通知配置列表
type SysNotifyConfigListInp struct {
	form.PageReq
	Id        int64         `json:"id"        dc:"主键ID"`
	Status    int           `json:"status"    dc:"状态"`
	CreatedAt []*gtime.Time `json:"createdAt" dc:"创建时间"`
}

func (in *SysNotifyConfigListInp) Filter(ctx context.Context) (err error) {
	return
}

type SysNotifyConfigListModel struct {
	Id        int64       `json:"id"        dc:"主键ID"`
	Category  int         `json:"category"  dc:"通知分类"`
	Name      string      `json:"name"      dc:"通知名称"`
	SignText  string      `json:"signText"  dc:"短信签名"`
	Content   string      `json:"content"   dc:"短信内容"`
	Mobiles   string      `json:"mobiles"   dc:"通知号码"`
	MemberId  int64       `json:"memberId"  dc:"关联用户ID"`
	Status    int         `json:"status"    dc:"状态"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" dc:"更新时间"`
}

// SysNotifyConfigExportModel 导出系统_审核通知配置
type SysNotifyConfigExportModel struct {
	Id        int64       `json:"id"        dc:"主键ID"`
	Category  int         `json:"category"  dc:"通知分类"`
	Name      string      `json:"name"      dc:"通知名称"`
	SignText  string      `json:"signText"  dc:"短信签名"`
	Content   string      `json:"content"   dc:"短信内容"`
	Mobiles   string      `json:"mobiles"   dc:"通知号码"`
	MemberId  int64       `json:"memberId"  dc:"关联用户ID"`
	Status    int         `json:"status"    dc:"状态"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" dc:"更新时间"`
}

// SysNotifyConfigStatusInp 更新系统_审核通知配置状态
type SysNotifyConfigStatusInp struct {
	Id     int64 `json:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	Status int   `json:"status" dc:"状态"`
}

func (in *SysNotifyConfigStatusInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("主键ID不能为空")
		return
	}

	if in.Status <= 0 {
		err = gerror.New("状态不能为空")
		return
	}

	if !validate.InSlice(consts.StatusSlice, in.Status) {
		err = gerror.New("状态不正确")
		return
	}
	return
}

type SysNotifyConfigStatusModel struct{}