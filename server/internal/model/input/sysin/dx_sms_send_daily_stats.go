// Package sysin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2025 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sysin

import (
	"context"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// DxSmsSendDailyStatsListInp 获取短信报表列表
type DxSmsSendDailyStatsListInp struct {
	form.PageReq
	StatDate            []*gtime.Time `json:"statDate"            dc:"统计日期"`
	AdminMemberRealName string        `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string        `json:"adminMemberUsername" dc:"子账号"`
	SmsType             int           `json:"smsType"             dc:"短信类型"`
	StatDimension       string        `json:"statDimension"       dc:"统计维度：day-按天，month-按月"`
}

func (in *DxSmsSendDailyStatsListInp) Filter(ctx context.Context) (err error) {
	return
}

type DxSmsSendDailyStatsListModel struct {
	Id                  int64             `json:"id"                  dc:"ID"`
	MemberId            int64             `json:"memberId"   dc:"用户ID"`
	StatDate            *gtime.Time       `json:"statDate"            dc:"统计日期"`
	SmsType             int               `json:"smsType"             dc:"短信类型"`
	TotalCount          int               `json:"totalCount"          dc:"发送量"`
	SuccessCount        int               `json:"successCount"        dc:"成功量"`
	FeeCount            int               `json:"feeCount"            dc:"费用量"`
	FailCount           int               `json:"failCount"           dc:"失败量"`
	UnknownCount        int               `json:"unknownCount"        dc:"未知量"`
	SuccessRate         float64           `json:"successRate"         dc:"成功率"`
	CreatedAt           *gtime.Time       `json:"createdAt"           dc:"创建时间"`
	AdminMemberRealName string            `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string            `json:"adminMemberUsername" dc:"子账号"`
	MemberBySumma       *hook.MemberSumma `json:"memberBySumma" dc:"创建人摘要信息"`
}

// DxSmsSendDailyStatsExportModel 导出短信报表
type DxSmsSendDailyStatsExportModel struct {
	Id                  int64             `json:"id"                  dc:"ID"`
	StatDate            *gtime.Time       `json:"statDate"            dc:"统计日期"`
	SmsType             int               `json:"smsType"             dc:"短信类型"`
	TotalCount          int               `json:"totalCount"          dc:"发送量"`
	SuccessCount        int               `json:"successCount"        dc:"成功量"`
	FeeCount            int               `json:"feeCount"            dc:"费用量"`
	FailCount           int               `json:"failCount"           dc:"失败量"`
	UnknownCount        int               `json:"unknownCount"        dc:"未知量"`
	SuccessRate         float64           `json:"successRate"         dc:"成功率"`
	CreatedAt           *gtime.Time       `json:"createdAt"           dc:"创建时间"`
	AdminMemberRealName string            `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string            `json:"adminMemberUsername" dc:"子账号"`
	MemberBySumma       *hook.MemberSumma `json:"memberBySumma" dc:"创建人摘要信息"`
}

// DxSmsSendDailyStatsSummaryInp 获取短信报表统计汇总
type DxSmsSendDailyStatsSummaryInp struct {
	StatDate            []*gtime.Time `json:"statDate"            dc:"统计日期"`
	AdminMemberRealName string        `json:"adminMemberRealName" dc:"企业名称"`
	AdminMemberUsername string        `json:"adminMemberUsername" dc:"子账号"`
	SmsType             int           `json:"smsType"             dc:"短信类型"`
	StatDimension       string        `json:"statDimension"       dc:"统计维度：day-按天，month-按月"`
}

// DxSmsSendDailyStatsSummaryModel 短信报表统计汇总输出
type DxSmsSendDailyStatsSummaryModel struct {
	TotalCount   int     `json:"totalCount"   dc:"发送量"`
	SuccessCount int     `json:"successCount" dc:"成功量"`
	FeeCount     int     `json:"feeCount"     dc:"成功条数"`
	FailCount    int     `json:"failCount"    dc:"失败量"`
	UnknownCount int     `json:"unknownCount" dc:"未知量"`
	SuccessRate  float64 `json:"successRate"  dc:"成功率"`
}
