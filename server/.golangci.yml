## This file contains all available configuration options
## with their default values.

# See https://github.com/golangci/golangci-lint#config-file
# See https://golangci-lint.run/usage/configuration/

# Options for analysis running.
run:
  # Exit code when at least one issue was found.
  # Default: 1
  issues-exit-code: 2

  # Include test files or not.
  # Default: true
  tests: false
  go: "1.20"

  # Which dirs to skip: issues from them won't be reported.
  # Can use regexp here: `generated.*`, regexp is applied on full path.
  # Default value is empty list,
  # but default dirs are skipped independently of this option's value (see skip-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work on Windows.
  # skip-dirs:
  #   - internal/library/hggen/internal

  # Which files to skip: they will be analyzed, but issues from them won't be reported.
  # Default value is empty list,
  # but there is no need to include all autogenerated files,
  # we confidently recognize autogenerated files.
  # If it's not please let us know.
  # "/" will be replaced by current OS file path separator to properly work on Windows.
  # skip-files: []


# Main linters configurations.
# See https://golangci-lint.run/usage/linters
linters:
  # Disable all default enabled linters.
  disable-all: true
  # Custom enable linters we want to use.
  enable:
    - errcheck      # Errcheck is a program for checking for unchecked errors in go programs.
    - errchkjson    # Checks types passed to the json encoding functions. Reports unsupported types and optionally reports occasions, where the check for the returned error can be omitted.
    - funlen        # Tool for detection of long functions
    - goconst       # Finds repeated strings that could be replaced by a constant
    - gocritic      # Provides diagnostics that check for bugs, performance and style issues.
#    - gofmt         # Gofmt checks whether code was gofmt-ed. By default this tool runs with -s option to check for code simplification
    - gosimple      # Linter for Go source code that specializes in simplifying code
    - govet         # Vet examines Go source code and reports suspicious constructs, such as Printf calls whose arguments do not align with the format string
    - misspell      # Finds commonly misspelled English words in comments
    - nolintlint    # Reports ill-formed or insufficient nolint directives
    - revive        # Fast, configurable, extensible, flexible, and beautiful linter for Go. Drop-in replacement of golint.
    - staticcheck   # It's a set of rules from staticcheck. It's not the same thing as the staticcheck binary.
    - typecheck     # Like the front-end of a Go compiler, parses and type-checks Go code
    - usestdlibvars # A linter that detect the possibility to use variables/constants from the Go standard library.
    - whitespace    # Tool for detection of leading and trailing whitespace


issues:
  exclude-rules:
    # helpers in tests often (rightfully) pass a *testing.T as their first argument
    - path: _test\.go
      text: "context.Context should be the first parameter of a function"
      linters:
        - revive
    # Yes, they are, but it's okay in a test
    - path: _test\.go
      text: "exported func.*returns unexported type.*which can be annoying to use"
      linters:
        - revive
    # https://github.com/go-critic/go-critic/issues/926
    - linters:
        - gocritic
      text: "unnecessaryDefer:"
  exclude-dirs:
    - internal/library/hggen/internal
  exclude-files: []
    

# https://golangci-lint.run/usage/linters
linters-settings:
  # https://golangci-lint.run/usage/linters/#misspell
  misspell:
    locale: US
    ignore-words:
      - cancelled

  # https://golangci-lint.run/usage/linters/#revive
  # https://github.com/mgechev/revive/blob/master/RULES_DESCRIPTIONS.md
  revive:
    ignore-generated-header: true
    severity: error
    rules:
      - name: atomic
      - name: line-length-limit
        severity: error
        arguments: [ 1024 ]
      - name: unhandled-error
        severity: warning
        disabled: true
        arguments: []
      - name: var-naming
        severity: warning
        disabled: true
        arguments:
          # AllowList
          - [ "ID","URL","IP","HTTP","JSON","API","UID","Id","Api","Uid","Http","Json","Ip","Url" ]
          # DenyList
          - [ "VM" ]
      - name: string-format
        severity: warning
        disabled: false
        arguments:
          - - 'core.WriteError[1].Message'
            - '/^([^A-Z]|$)/'
            - must not start with a capital letter
          - - 'fmt.Errorf[0]'
            - '/(^|[^\.!?])$/'
            - must not end in punctuation
          - - panic
            - '/^[^\n]*$/'
            - must not contain line breaks
      - name: function-result-limit
        severity: warning
        disabled: false
        arguments: [ 4 ]

  # https://golangci-lint.run/usage/linters/#funlen
  funlen:
    # Checks the number of lines in a function.
    # If lower than 0, disable the check.
    # Default: 60
    lines: 330
    # Checks the number of statements in a function.
    # If lower than 0, disable the check.
    # Default: 40
    statements: -1

  # https://golangci-lint.run/usage/linters/#goconst
  goconst:
    # Minimal length of string constant.
    # Default: 3
    min-len: 4
    # Minimum occurrences of constant string count to trigger issue.
    # Default: 3
    # For subsequent optimization, the value is reduced.
    min-occurrences: 30
    # Ignore test files.
    # Default: false
    ignore-tests: true
    # Look for existing constants matching the values.
    # Default: true
    match-constant: false
    # Search also for duplicated numbers.
    # Default: false
    numbers: true
    # Minimum value, only works with goconst.numbers
    # Default: 3
    min: 5
    # Maximum value, only works with goconst.numbers
    # Default: 3
    max: 20
    # Ignore when constant is not used as function argument.
    # Default: true
    ignore-calls: false

  # https://golangci-lint.run/usage/linters/#gocritic
  gocritic:
    disabled-checks:
      - ifElseChain
      - assignOp
      - appendAssign
      - singleCaseSwitch
      - regexpMust
      - typeSwitchVar
      - elseif

  # https://golangci-lint.run/usage/linters/#gosimple
  gosimple:
    # Select the Go version to target.
    # Default: 1.13
    # Deprecated: use the global `run.go` instead.
    # go: "1.15"
    # Sxxxx checks in https://staticcheck.io/docs/configuration/options/#checks
    # Default: ["*"]
    checks: [
      "all", "-S1000", "-S1001", "-S1002", "-S1008", "-S1009", "-S1016", "-S1023", "-S1025", "-S1029", "-S1034", "-S1040"
    ]

  # https://golangci-lint.run/usage/linters/#govet
  govet:
    # Report about shadowed variables.
    # Default: false
    # check-shadowing: true
    # Settings per analyzer.
    settings:
      # Analyzer name, run `go tool vet help` to see all analyzers.
      printf:
        # Comma-separated list of print function names to check (in addition to default, see `go tool vet help printf`).
        # Default: []
        funcs:
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf
        # shadow:
        # Whether to be strict about shadowing; can be noisy.
        # Default: false
        # strict: false
      unusedresult:
        # Comma-separated list of functions whose results must be used
        # (in addition to defaults context.WithCancel,context.WithDeadline,context.WithTimeout,context.WithValue,
        # errors.New,fmt.Errorf,fmt.Sprint,fmt.Sprintf,sort.Reverse)
        # Default []
        funcs:
          - pkg.MyFunc
          - context.WithCancel
        # Comma-separated list of names of methods of type func() string whose results must be used
        # (in addition to default Error,String)
        # Default []
        stringmethods:
          - MyMethod
    # Enable all analyzers.
    # Default: false
    enable-all: true
    # Disable analyzers by name.
    # Run `go tool vet help` to see all analyzers.
    # Default: []
    disable:
      - asmdecl
      - assign
      - atomic
      - atomicalign
      - bools
      - buildtag
      - cgocall
      - composites
      - copylocks
      - deepequalerrors
      - errorsas
      - fieldalignment
      - findcall
      - framepointer
      - httpresponse
      - ifaceassert
      - loopclosure
      - lostcancel
      - nilfunc
      - nilness
      - reflectvaluecompare
      - shift
      - shadow
      - sigchanyzer
      - sortslice
      - stdmethods
      - stringintconv
      - structtag
      - testinggoroutine
      - tests
      - unmarshal
      - unreachable
      - unsafeptr
      - unusedwrite

  # https://golangci-lint.run/usage/linters/#staticcheck
  staticcheck:
    # Select the Go version to target.
    # Default: "1.13"
    # Deprecated: use the global `run.go` instead.
    # go: "1.15"
    # SAxxxx checks in https://staticcheck.io/docs/configuration/options/#checks
    # Default: ["*"]
    checks: [ "all","-SA1019","-SA4015","-SA1029","-SA1016","-SA9003","-SA4006","-SA6003" ]

  # https://golangci-lint.run/usage/linters/#gofmt
  gofmt:
    # Simplify code: gofmt with `-s` option.
    # Default: true
    simplify: true
    # Apply the rewrite rules to the source before reformatting.
    # https://pkg.go.dev/cmd/gofmt
    # Default: []
    rewrite-rules: [ ]
      # - pattern: 'interface{}'
      #   replacement: 'any'
      # - pattern: 'a[b:len(a)]'
    #   replacement: 'a[b:]'