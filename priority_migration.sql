-- 为重发通道表添加优先级字段的迁移脚本
-- 执行时间：请在系统维护时间执行

-- 1. 添加优先级字段
ALTER TABLE `hg_admin_company_resend_channel` 
ADD COLUMN `priority` int(11) NOT NULL DEFAULT 1 COMMENT '优先级(数值越小优先级越高)' 
AFTER `channel_id`;

-- 2. 为现有数据设置优先级（按创建时间顺序）
-- 这个脚本会为每个企业的重发通道按创建时间顺序设置优先级
SET @row_number = 0;
SET @prev_member_id = 0;

UPDATE `hg_admin_company_resend_channel` t1
JOIN (
    SELECT 
        id,
        member_id,
        @row_number := CASE 
            WHEN @prev_member_id = member_id THEN @row_number + 1 
            ELSE 1 
        END AS priority,
        @prev_member_id := member_id
    FROM `hg_admin_company_resend_channel`
    ORDER BY member_id, created_at ASC
) t2 ON t1.id = t2.id
SET t1.priority = t2.priority;

-- 3. 验证数据（可选，用于检查迁移结果）
-- SELECT member_id, channel_id, priority, created_at 
-- FROM `hg_admin_company_resend_channel` 
-- ORDER BY member_id, priority; 