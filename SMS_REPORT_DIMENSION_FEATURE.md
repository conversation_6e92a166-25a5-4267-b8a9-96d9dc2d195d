# 短信报表统计维度功能

## 功能概述

为短信报表增加了统计维度选择功能，用户可以选择按天或按月查看短信发送统计数据。

### 新增功能
- **统计维度选择**：在搜索表单中添加了"按天"和"按月"的单选按钮
- **按月聚合统计**：按月统计时会将同一月份的数据进行聚合
- **智能日期显示**：按月统计时只显示年月，按天统计时显示完整日期
- **导出功能优化**：导出文件名会根据统计维度显示不同的标识

## 实现详情

### 前端修改

#### 1. web/src/views/dxSmsSendDailyStats/model.ts
- 新增 `statDimensionOptions` 统计维度选项
- 在搜索表单中添加统计维度单选按钮组件
- 修改统计日期列的渲染逻辑，根据数据格式智能显示

**关键代码**：
```typescript
// 统计维度选项
export const statDimensionOptions = [
  { label: '按天', value: 'day' },
  { label: '按月', value: 'month' },
];

// 搜索表单中的统计维度字段
{
  field: 'statDimension',
  component: 'NRadioGroup',
  label: '统计维度',
  defaultValue: 'day',
  componentProps: {
    options: statDimensionOptions,
  },
}
```

#### 2. web/src/views/dxSmsSendDailyStats/index.vue
- 修改 `loadDataTable` 函数，确保统计维度参数正确传递
- 修改 `handleExport` 函数，导出时也传递统计维度参数
- 默认统计维度设置为"按天"

### 后端修改

#### 1. server/internal/model/input/sysin/dx_sms_send_daily_stats.go
- 在 `DxSmsSendDailyStatsListInp` 结构体中添加 `StatDimension` 字段

**关键代码**：
```go
type DxSmsSendDailyStatsListInp struct {
    form.PageReq
    StatDate            []*gtime.Time `json:"statDate"            dc:"统计日期"`
    AdminMemberRealName string        `json:"adminMemberRealName" dc:"企业名称"`
    AdminMemberUsername string        `json:"adminMemberUsername" dc:"子账号"`
    SmsType             int           `json:"smsType"             dc:"短信类型"`
    StatDimension       string        `json:"statDimension"       dc:"统计维度：day-按天，month-按月"`
}
```

#### 2. server/internal/logic/sys/dx_sms_send_daily_stats.go
- 重构 `List` 方法，根据统计维度调用不同的查询方法
- 新增 `listByDay` 方法，保持原有的按天查询逻辑
- 新增 `listByMonth` 方法，实现按月聚合查询
- 修改 `Export` 方法，支持统计维度并优化导出文件名

**按月聚合查询关键逻辑**：
```go
// 按月聚合的字段选择
selectFields := []string{
    "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m-01') as stat_date",
    "SUM(" + dao.DxSmsSendDailyStats.Columns().TotalCount + ") as total_count",
    "SUM(" + dao.DxSmsSendDailyStats.Columns().SuccessCount + ") as success_count",
    // ... 其他聚合字段
}

// 按月分组
groupFields := []string{
    "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m')",
    dao.DxSmsSendDailyStats.Columns().MemberId,
    dao.DxSmsSendDailyStats.Columns().SmsType,
}
mod = mod.Group(groupFields...)
```

## 功能特点

### 1. 用户体验优化
- **默认按天显示**：保持与原有功能的兼容性
- **直观的选择界面**：使用单选按钮组，用户可以清楚地看到当前选择的统计维度
- **智能日期显示**：按月统计时自动显示为"YYYY-MM"格式

### 2. 数据聚合准确性
- **按月聚合**：将同一用户、同一短信类型在同一月份的所有数据进行聚合
- **成功率重新计算**：按月统计时重新计算成功率，确保数据准确性
- **保持用户隔离**：聚合时仍然保持用户和短信类型的区分

### 3. 导出功能增强
- **文件名标识**：导出文件名会显示"按天"或"按月"标识
- **数据一致性**：导出的数据与页面显示的数据完全一致

## 使用说明

### 前端操作
1. 打开短信报表页面
2. 在搜索表单顶部选择统计维度（默认为"按天"）
3. 设置其他筛选条件（日期范围、短信类型等）
4. 点击查询查看结果
5. 可以导出对应维度的统计数据

### 数据说明
- **按天统计**：显示每天的详细数据，日期格式为"YYYY-MM-DD"
- **按月统计**：显示每月的汇总数据，日期格式为"YYYY-MM"，数据为该月所有天数的累计值

## 技术实现亮点

### 1. 向后兼容
- 保持原有API接口不变
- 新增参数有默认值，不影响现有功能
- 前端默认选择"按天"，与原有行为一致

### 2. 数据库查询优化
- 使用SQL的DATE_FORMAT函数进行日期格式化
- 使用GROUP BY进行数据聚合，提高查询效率
- 保持原有的排序和分页逻辑

### 3. 代码结构清晰
- 将按天和按月的查询逻辑分离到不同方法
- 保持代码的可读性和可维护性
- 统一的错误处理和返回格式

## 测试建议

### 功能测试
1. **基础功能**：测试按天和按月的切换是否正常
2. **数据准确性**：对比按天数据的月度汇总与按月统计的结果
3. **筛选功能**：测试各种筛选条件在不同统计维度下的效果
4. **导出功能**：测试导出的数据是否与页面显示一致

### 性能测试
1. **大数据量**：测试大量数据下按月聚合的性能
2. **并发访问**：测试多用户同时使用不同统计维度的情况

## 后续优化建议

1. **缓存机制**：可以考虑对按月统计的结果进行缓存
2. **更多维度**：未来可以扩展支持按周、按季度等统计维度
3. **图表展示**：可以添加图表组件，更直观地展示统计数据
4. **数据对比**：可以添加同比、环比等对比功能
