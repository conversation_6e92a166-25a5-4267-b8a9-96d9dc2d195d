# 短信发送记录失败重发明细功能说明

## 功能概述

在短信发送记录详情页面添加失败重发明细显示功能，展示该短信记录的失败重发历史详情。

## 实现功能

### 1. 后端实现

#### 数据模型
- **实体模型**: `server/internal/model/entity/dx_sms_failure_log.go`
- **DAO模型**: `server/internal/dao/dx_sms_failure_log.go` 和 `server/internal/dao/internal/dx_sms_failure_log.go`
- **输入模型**: `server/internal/model/input/sysin/dx_sms_failure_log.go`

#### API接口
- **接口定义**: `server/api/admin/dxsmssendrecord/dxsmssendrecord.go`
  - 添加了 `FailureLogListReq` 和 `FailureLogListRes` 结构
- **控制器**: `server/internal/controller/admin/sys/dx_sms_send_record.go`
  - 添加了 `FailureLogList` 方法
- **服务接口**: `server/internal/service/sys.go`
  - 在 `ISysDxSmsSendRecord` 接口中添加了 `FailureLogList` 方法

#### 业务逻辑
- **逻辑层**: `server/internal/logic/sys/dx_sms_send_record.go`
  - 实现了 `FailureLogList` 方法，支持与短信通道表的关联查询
  - 修改了 `View` 方法，在获取详情时自动查询失败重发记录

### 2. 前端实现

#### API调用
- **API接口**: `web/src/api/dxSmsSendRecord/index.ts`
  - 添加了 `FailureLogList` API方法

#### 页面展示
- **详情页面**: `web/src/views/dxSmsSendRecord/view.vue`
  - 在详情页面底部添加失败重发明细表格
  - 表格显示批次号、手机号、失败原因、通道名称、失败时间、创建时间

#### 数据模型
- **前端模型**: `web/src/views/dxSmsSendRecord/model.ts`
  - 在 `State` 类中添加了 `failureLogs` 字段
  - 添加了 `FailureLog` 类型定义

### 3. 数据库结构

数据库表 `hg_dx_sms_failure_log` 结构如下：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| original_record_id | bigint(20) | 原始记录ID（关联hg_dx_sms_send_record.id） |
| member_id | bigint(20) | 用户ID |
| batch_no | varchar(32) | 批次号 |
| mobile | varchar(20) | 手机号码 |
| channel_id | bigint(20) | 通道ID |
| failed_reason | varchar(255) | 失败原因 |
| failed_time | datetime | 失败时间 |
| created_at | datetime | 创建时间 |

表索引：
- primary: id
- idx_original_record_id: original_record_id
- idx_member_id: member_id  
- idx_batch_no: batch_no
- idx_mobile: mobile
- idx_failed_time: failed_time

## 使用说明

### 功能入口
1. 进入短信发送记录列表页面
2. 点击任意记录的"查看"按钮
3. 在详情页面底部查看"失败重发明细"部分

### 显示逻辑
- 只有当该短信记录存在失败重发记录时，才会显示"失败重发明细"部分
- 失败重发记录通过 `original_record_id` 字段与原始短信记录关联
- 表格显示该记录的所有失败重发历史

### 字段说明
- **批次号**: 失败重发对应的批次号
- **手机号**: 失败的手机号码
- **失败原因**: 失败的具体原因描述
- **通道名称**: 失败时使用的短信通道名称
- **失败时间**: 记录失败的时间
- **创建时间**: 该失败记录的创建时间

## 技术特点

1. **自动集成**: 在查看短信详情时自动加载失败重发记录，无需额外操作
2. **关联查询**: 支持与短信通道表的左连接查询，显示通道名称
3. **错误处理**: 失败重发记录查询失败不会影响主要详情的显示
4. **分区支持**: 支持分区表结构的短信记录查询
5. **只读显示**: 功能为纯展示功能，不涉及数据的创建或修改

## 注意事项

1. 失败重发记录的生成由其他程序负责，本功能只负责查询和显示
2. 支持现有的短信记录分区表结构
3. 功能具有良好的向下兼容性，对于没有失败重发记录的短信，不会显示该部分
4. 查询性能已优化，通过索引和左连接确保查询效率 