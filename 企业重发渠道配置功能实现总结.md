# 企业重发渠道配置功能实现总结

## 功能概述
为企业管理系统实现重发渠道配置功能，允许超级管理员和管理员为企业配置多个重发通道，当主通道发送失败时可自动使用重发通道重试。

## 技术架构
- **后端**: GoFrame v2 框架
- **前端**: Vue 3 + TypeScript + Naive UI
- **数据库**: 支持 MySQL 和 SQLite

## 实现详情

### 1. 数据库层

#### 1.1 创建重发渠道关联表
**文件**: `server/storage/data/hotgo.sql` 和 `server/storage/data/sqlite/tables.sql`

```sql
-- MySQL版本
CREATE TABLE IF NOT EXISTS `hg_admin_company_resend_channel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '企业ID，关联hg_admin_company表',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道ID，关联短信渠道表',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_channel` (`member_id`, `channel_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_channel_id` (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业重发渠道配置表';

-- SQLite版本
CREATE TABLE IF NOT EXISTS "hg_admin_company_resend_channel" (
  "id" INTEGER PRIMARY KEY AUTOINCREMENT,
  "member_id" INTEGER NOT NULL,
  "channel_id" INTEGER NOT NULL,
  "created_at" DATETIME DEFAULT CURRENT_TIMESTAMP,
  "updated_at" DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE("member_id", "channel_id")
);
```

#### 1.2 表设计特点
- 使用 member_id 关联企业表 (hg_admin_company)
- 使用 channel_id 关联短信渠道表
- 建立唯一约束防止重复配置
- 支持多对多关系（一个企业可配置多个重发渠道）

### 2. 后端数据访问层

#### 2.1 DAO层
**文件**: 
- `server/internal/dao/internal/admin_company_resend_channel.go`
- `server/internal/dao/admin_company_resend_channel.go`

自动生成的数据访问对象，提供基础的CRUD操作。

#### 2.2 DO层
**文件**: `server/internal/model/do/admin_company_resend_channel.go`

定义数据对象结构，用于数据库操作。

#### 2.3 Entity层
**文件**: `server/internal/model/entity/admin_company_resend_channel.go`

定义实体结构，对应数据库表结构。

### 3. 后端业务逻辑层

#### 3.1 输入参数扩展
**文件**: `server/internal/model/input/sysin/admin_company.go`

```go
// 在 AdminCompanyEditInp 和 AdminCompanyViewModel 中添加
ResendChannels []int64 `json:"resendChannels" dc:"重发渠道"`
```

#### 3.2 业务逻辑实现
**文件**: `server/internal/logic/sys/admin_company.go`

**核心方法**:

1. **saveResendChannels()** - 保存重发渠道配置
   - 先删除原有配置
   - 批量插入新配置
   - 使用事务确保数据一致性

2. **getResendChannels()** - 获取重发渠道配置
   - 根据企业ID查询配置的渠道列表
   - 返回渠道ID数组

3. **Edit()** - 编辑企业信息（已修改）
   - 在保存企业信息后同步保存重发渠道配置
   - 支持新增和编辑场景

4. **View()** - 查看企业详情（已修改）
   - 在返回企业信息时包含重发渠道配置

5. **Delete()** - 删除企业（已修改）
   - 在删除企业时清理相关重发渠道配置

### 4. 前端实现

#### 4.1 数据模型扩展
**文件**: `web/src/views/adminCompany/model.ts`

```typescript
export class State {
  // ... 其他字段
  public resendChannels: number[] = []; // 重发通道
}
```

#### 4.2 用户权限检查
**文件**: `web/src/store/modules/user.ts`

扩展了 UserInfoState 接口，添加 roleId 字段用于权限判断。

#### 4.3 编辑页面增强
**文件**: `web/src/views/adminCompany/edit.vue`

**新增功能**:
- 权限检查：只有超级管理员（roleId=1）和管理员（roleId=2）可见
- 重发渠道选择组件：
  - 复用现有的短信渠道树选择组件
  - 支持多选
  - 最多显示3个标签
  - 提供使用说明文字

```vue
<n-gi span="1">
  <n-form-item label="重发通道" path="resendChannels" v-if="hasResendChannelPermission">
    <n-tree-select
      placeholder="请选择重发通道"
      v-model:value="formValue.resendChannels"
      :options="channelTreeOption"
      key-field="id"
      label-field="channelName"
      multiple
      clearable
      filterable
      default-expand-all
      show-path
      :max-tag-count="3"
    />
    <n-text depth="3" style="font-size: 12px; margin-top: 4px; display: block;">
      配置重发通道后，当主通道发送失败时将自动使用重发通道重试
    </n-text>
  </n-form-item>
</n-gi>
```

#### 4.4 查看页面增强
**文件**: `web/src/views/adminCompany/view.vue`

在企业详情中显示配置的重发渠道：
- 权限控制显示
- 以标签形式展示渠道ID
- 提供功能说明

```vue
<n-descriptions-item v-if="hasResendChannelPermission && formValue.resendChannels && formValue.resendChannels.length > 0">
  <template #label>重发通道</template>
  <n-space>
    <n-tag v-for="channelId in formValue.resendChannels" :key="channelId" type="info" size="small">
      通道ID: {{ channelId }}
    </n-tag>
  </n-space>
  <n-text depth="3" style="font-size: 12px; margin-top: 4px; display: block;">
    当主通道发送失败时将自动使用这些通道重试
  </n-text>
</n-descriptions-item>
```

## 权限控制

### 角色定义
- **SuperAdmin (roleId: 1)**: 超级管理员
- **Admin (roleId: 2)**: 管理员
- **CompanyManage (roleId: 222)**: 企业管理员
- **CompanyUser (roleId: 223)**: 企业用户

### 权限策略
重发渠道配置功能仅对超级管理员和管理员开放，通过前端计算属性实现权限控制：

```typescript
const hasResendChannelPermission = computed(() => {
  const userInfo = userStore.getUserInfo;
  if (!userInfo) return false;
  return userInfo.roleId === 1 || userInfo.roleId === 2;
});
```

## 数据流程

### 保存流程
1. 前端收集重发渠道ID数组
2. 提交到后端 Edit 接口
3. 后端事务性保存企业信息和重发渠道配置
4. 返回成功结果

### 查询流程
1. 前端调用 View 接口
2. 后端查询企业信息和重发渠道配置
3. 组装完整数据返回前端
4. 前端根据权限显示配置信息

### 删除流程
1. 删除企业时自动清理重发渠道配置
2. 保证数据一致性

## 技术特点

1. **权限控制**: 基于角色的访问控制，确保只有授权用户可以配置
2. **数据完整性**: 使用外键约束和唯一约束保证数据一致性
3. **事务安全**: 使用数据库事务确保数据操作的原子性
4. **组件复用**: 复用现有的渠道选择组件，保持UI一致性
5. **跨数据库支持**: 同时支持MySQL和SQLite数据库
6. **响应式设计**: 前端组件支持响应式布局

## 测试验证

前端构建测试通过，代码语法正确，可以正常编译和运行。

## 使用说明

1. **配置权限**: 确保当前用户是超级管理员或管理员
2. **选择渠道**: 在企业编辑页面的"重发通道"字段选择需要的渠道
3. **保存配置**: 点击确定保存配置
4. **查看配置**: 在企业详情页面可以查看已配置的重发渠道
5. **业务逻辑**: 系统会在主通道发送失败时自动使用配置的重发渠道重试

## 注意事项

1. 重发渠道配置是可选的，不配置不影响正常使用
2. 建议配置的重发渠道与主通道使用不同的服务商，提高成功率
3. 重发渠道的使用策略需要在短信发送逻辑中实现
4. 定期检查渠道状态，确保配置的渠道可用

## 扩展建议

1. **渠道优先级**: 可以为重发渠道配置优先级顺序
2. **失败策略**: 可以配置重试次数和重试间隔
3. **监控统计**: 添加重发渠道使用情况统计
4. **渠道健康检查**: 定期检查渠道可用性
5. **动态配置**: 支持运行时动态调整重发策略 