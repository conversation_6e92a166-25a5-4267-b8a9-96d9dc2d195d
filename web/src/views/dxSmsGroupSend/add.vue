<template>
	<div class="add_box">
		<!-- <n-modal v-model:show="showModal" :mask-closable="false" :show-icon="false" preset="dialog"
			transform-origin="center" :title="formValue.id > 0 ? '编辑短信群发 #' + formValue.id : '添加短信群发'" :style="{
				width: dialogWidth,
			}"> -->
		<div class="add">
			<n-scrollbar style="max-height: 87vh" class="pr-5">
				<n-spin :show="loading" description="请稍候...">
					<n-form ref="formRef" :model="formValue" :rules="rules"
						:label-placement="settingStore.isMobile ? 'top' : 'left'" :label-width="100" class="py-4">
						<n-grid cols="1 s:1 m:1 l:1 xl:1 2xl:1" responsive="screen">
							<n-gi span="1">
								<n-form-item label="短信模式" path="smsMode">
									<n-select v-model:value="formValue.smsMode"
										:options="dict.getOptionUnRef('sms_mode')" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item v-show="formValue.smsMode === 2" label="短信模板" path="templateId">
									<n-select v-model:value="formValue.templateId" :options="templateOption" />
									<n-button type="primary" @click="downloadExcelFile" class="min-left-space">
										<template #icon>
											<n-icon>
												<DownloadOutlined />
											</n-icon>
										</template>
										下载模板
									</n-button>
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item label="短信签名" path="signId">
									<n-select v-model:value="formValue.signId" :options="signOption" />
								</n-form-item>
							</n-gi>
							<!-- <n-gi span="1">
								<n-form-item label="通道" path="channelId">
									<n-input-number placeholder="请输入通道" v-model:value="formValue.channelId" />
								</n-form-item>
							</n-gi> -->
							<n-gi span="1">
								<!-- 批量 -->
								<n-form-item label="发送号码" path="mobiles" v-show="formValue.smsMode === 1">
									<n-space vertical style="width: 100%;">
										<n-upload directory-dnd 
											:custom-request="handleUpload" 
											:on-remove="handleRemove"
											name="file" 
											:disabled="uploadStatus != 0 && uploadStatus != 3" 
											:max="1"
											accept=".txt">
											<n-upload-dragger>
												<div style="margin-bottom: 12px">
													<n-icon size="48" :depth="3">
														<FileAddOutlined />
													</n-icon>
												</div>
												<template v-if="uploadStatus == 0 || uploadStatus == 3">
													<n-text style="font-size: 16px">点击或者拖动TXT文档到该区域来上传</n-text>
													<n-p depth="3" style="margin: 8px 0 0 0">支持大文件分片上传，支持断点续传</n-p>
												</template>
												<template v-else-if="uploadStatus == 1">
													<span style="font-weight: 600">解析中，请稍候...</span>
												</template>
												<template v-else-if="uploadStatus == 2">
													<span style="font-weight: 600">正在上传({{ progress }}%)...</span>
													<n-p depth="3" style="margin: 8px 0 0 0">文件大小：{{ sizeFormat }}</n-p>
												</template>
											</n-upload-dragger>
										</n-upload>
										<n-space vertical v-if="checkResult.successCount > 0 || checkResult.failCount > 0 || checkResult.duplicateCount > 0">
											<n-alert type="info" style="margin-top: 10px;">
												<template #header>
													文件检查结果
												</template>
												<n-space vertical>
													<span>成功条数：{{ checkResult.successCount }}</span>
													<span>失败条数：{{ checkResult.failCount }}</span>
													<span>重复条数：{{ checkResult.duplicateCount }}</span>
												</n-space>
											</n-alert>
										</n-space>
										<n-divider title-placement="left" style="font-size: 14px;">从通讯录选择</n-divider>
										<n-space>
											<n-button type="primary" @click="openContactBook">
												<template #icon>
													<n-icon><ContactsOutlined /></n-icon>
												</template>
												通讯录
											</n-button>
										</n-space>
										<n-space vertical v-if="selectedGroups.length > 0">
											<n-alert type="success" style="margin-top: 10px;">
												<template #header>
													<n-space justify="space-between" align="center">
														<span>已选择分组：{{ selectedGroups.length }} 个，共 {{ selectedGroups.reduce((total, group) => total + (group.count || 0), 0) }} 人</span>
														<n-button size="small" @click="clearAllGroups">
															清空所有
														</n-button>
													</n-space>
												</template>
												<div style="margin-top: 8px;">
													<n-space>
														<n-tag
															v-for="group in selectedGroups"
															:key="group.key"
															closable
															@close="removeGroup(group.key)"
															type="info"
														>
															{{ group.name }} ({{ group.count || 0 }}人)
														</n-tag>
													</n-space>
												</div>
											</n-alert>
										</n-space>
										<n-divider title-placement="left" style="font-size: 14px;">手动输入用户号码</n-divider>
										<n-input type="textarea" placeholder="发送号码之间请用英文逗号分隔"
											v-model:value="formValue.mobiles"
											@input="handleMobilesInput" />
										<n-space style="margin-top: 8px;">
											<n-button type="primary" @click="openContactSelector">
												<template #icon>
													<n-icon><ContactsOutlined /></n-icon>
												</template>
												从通讯录选择号码
											</n-button>
										</n-space>
									</n-space>
								</n-form-item>
								<!-- 模板 -->
								<n-form-item label="导入模板" path="mobiles" v-show="formValue.smsMode === 2">
									<n-space vertical style="width: 100%;">
										<n-upload directory-dnd 
											:custom-request="handleUpload" 
											:on-remove="handleRemove"
											:on-before-upload="beforeUpload"
											name="file" 
											:disabled="uploadStatus != 0 && uploadStatus != 3" 
											:max="1"
											accept=".xlsx,.xls">
											<n-upload-dragger>
												<div style="margin-bottom: 12px">
													<n-icon size="48" :depth="3">
														<FileAddOutlined />
													</n-icon>
												</div>
												<template v-if="uploadStatus == 0 || uploadStatus == 3">
													<n-text style="font-size: 16px">点击或者拖动Excel文档到该区域来上传</n-text>
													<n-p depth="3" style="margin: 8px 0 0 0">支持大文件分片上传，支持断点续传</n-p>
												</template>
												<template v-else-if="uploadStatus == 1">
													<span style="font-weight: 600">解析中，请稍候...</span>
												</template>
												<template v-else-if="uploadStatus == 2">
													<span style="font-weight: 600">正在上传({{ progress }}%)...</span>
													<n-p depth="3" style="margin: 8px 0 0 0">文件大小：{{ sizeFormat }}</n-p>
												</template>
											</n-upload-dragger>
										</n-upload>
										<n-space vertical v-if="checkResult.successCount > 0 || checkResult.failCount > 0 || checkResult.duplicateCount > 0">
											<n-alert type="info" style="margin-top: 10px;">
												<template #header>
													文件检查结果
												</template>
												<n-space vertical>
													<span>成功条数：{{ checkResult.successCount }}</span>
													<span>失败条数：{{ checkResult.failCount }}</span>
													<span>重复条数：{{ checkResult.duplicateCount }}</span>
												</n-space>
											</n-alert>
										</n-space>
									</n-space>
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item label="短信内容" path="content">
									<!-- <n-input type="textarea" placeholder="短信内容" v-model:value="formValue.content" :disabled="formValue.smsMode === 2" /> -->
									<div style="display: flex; flex-direction: column; width: 100%;">
										<n-input type="textarea" placeholder="短信内容" v-model:value="formValue.content"
											:disabled="formValue.smsMode === 2" />
										<span style="
											/* color: #ff4d4f;  */
											color: #999;
											margin-top: 5px;
											line-height: 1;
										">
											已输入字数：{{ formValue.content?.length || 0 }}，总字数：{{ getTotalLength()
											}}，计费条数：{{
												calculateFeeNum(formValue.content)
											}}
										</span>
									</div>
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item label="备注" path="remark">
									<n-input type="textarea" placeholder="备注" v-model:value="formValue.remark" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item label="发送模式" path="sendMode">
									<n-select v-model:value="formValue.sendMode"
										:options="dict.getOptionUnRef('send_mode')" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item v-show="formValue.sendMode === 2" label="定时发送时间" path="scheduleTime">
									<DatePicker v-model:formValue="formValue.scheduleTime" type="datetime" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item v-show="formValue.sendMode === 3" label="周期发送开始日期" path="cycleStartDate">
									<DatePicker v-model:formValue="formValue.cycleStartDate" type="date" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item v-show="formValue.sendMode === 3" label="周期发送结束日期" path="cycleEndDate">
									<DatePicker v-model:formValue="formValue.cycleEndDate" type="date" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item v-show="formValue.sendMode === 3" label="周期发送开始时间" path="cycleStartTime">
									<n-time-picker v-model:formValue="formValue.cycleStartTime" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item v-show="formValue.sendMode === 3" label="周期发送结束时间" path="cycleEndTime">
									<n-time-picker v-model:formValue="formValue.cycleEndTime" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item v-show="formValue.sendMode === 3" label="周期发送频次/天" path="cycleFreq">
									<n-input-number placeholder="请输入周期发送频次/天" v-model:value="formValue.cycleFreq" />
								</n-form-item>
							</n-gi>
							<n-gi span="1">
								<n-form-item label="">
									<n-space style="margin-left: 100px;">
										<n-button @click="closeForm">
											取消
										</n-button>
										<n-button type="info" :loading="formBtnLoading" @click="confirmForm">
											提交
										</n-button>
									</n-space>
								</n-form-item>
							</n-gi>
						</n-grid>
					</n-form>
				</n-spin>
			</n-scrollbar>
		</div>
		<div class="info" v-if="formValue.smsMode === 1">
			<p>注意事项</p>
			<p>1、单次提交不超过300000个号码;</p>
			<p>2、内容编辑完成请先检查屏蔽词再行发送;</p>
			<p>3、号码文件目前只支持TXT，格式为每行一个手机号码;</p>
			<p>4、请使用CTRL+V粘贴短信内容;</p>
			<p>5、汉字、数字、英文和标点符号都表示1个长度;</p>
			<p>6、短信内容70个字计费一条，超过70字为长短信，67个字计费一条;</p>
			<p>7、短信实际长度=短信签名+短信内容;</p>
			<p>8、内容长度包括符号;</p>
			<p>9、发送内容无需带签名;</p>
		</div>
		<div class="info" v-else>
			<p>注意事项</p>
			<p>1、内容编辑完成请先检查屏蔽词再行发送;</p>
			<p>2、只支持EXCEL文件格式;</p>
			<p>3、EXCEL文件数据第一列必须为手机号码;</p>
			<p>4、汉字、数字、英文和标,点符号都表示1个长度;</p>
			<p>5、短信内容70个字计费一条，超过70字为长短信，67个字计费一条!</p>
			<p>6、短信实际长度=短信签名+短信内容;</p>
			<p>7、内容长度包括符号;</p>
			<p>8、选择模板后点击下载Excel按钮可下载短信模板文件;</p>
			<p>9、请严格按照下载的Excel文件填写;</p>
		</div>
		<!-- <template #action>
				<n-space>
					<n-button @click="closeForm">
						取消
					</n-button>
					<n-button type="info" :loading="formBtnLoading" @click="confirmForm">
						确定
					</n-button>
				</n-space>
			</template> -->
		<!-- </n-modal> -->
	</div>

	<!-- 通讯录选择弹窗 -->
	<ContactBookModal
		v-model:show="showContactBook"
		@confirm="handleContactBookSelect"
	/>

	<!-- 联系人选择弹窗 -->
	<ContactSelectorModal
		v-model:show="showContactSelector"
		@confirm="handleContactSelect"
	/>
</template>

<script lang="ts" setup>
import { computed, ref, watch, onMounted } from 'vue';
import { useDictStore } from '@/store/modules/dict';
import { Edit, View, CheckFile } from '@/api/dxSmsGroupSend';
import { State, newState, rules, templateOption, loadTemplateOption, loadSignOption, signOption } from './model';
import DatePicker from '@/components/DatePicker/datePicker.vue';
import { useProjectSettingStore } from '@/store/modules/projectSetting';
import { useMessage } from 'naive-ui';
import { adaModalWidth } from '@/utils/hotgo';
import { DownloadExcel } from '@/api/dxSmsGroupTemplate';
import { NModal, UploadCustomRequestOptions, useDialog, UploadFileInfo } from 'naive-ui';
import { DownloadOutlined, FileAddOutlined, ContactsOutlined, TeamOutlined } from '@vicons/antd';
import { Attachment } from '@/components/FileChooser/src/model';
import SparkMD5 from 'spark-md5';
import { CheckMultipart, UploadPart } from '@/api/base';
import type { UploadFileParams } from '@/utils/http/axios/types';
import { useRouter } from 'vue-router';
import ContactBookModal from './components/ContactBookModal.vue';
import ContactSelectorModal from './components/ContactSelectorModal.vue';

const router = useRouter();

const emit = defineEmits(['reloadTable']);
const message = useMessage();
const settingStore = useProjectSettingStore();
const dict = useDictStore();
const loading = ref(false);
const showModal = ref(false);
const formValue = ref<State>(newState(null));
const formRef = ref<any>({});
const formBtnLoading = ref(false);
const dialogWidth = computed(() => {
	return adaModalWidth(840);
});
// 保存当前选中的签名文本
const currentSignText = ref('');

// 通讯录选择相关
const showContactBook = ref(false);
const selectedGroups = ref([]);

// 联系人选择相关
const showContactSelector = ref(false);

// 添加文件检查结果状态
const checkResult = ref({
	successCount: 0,
	failCount: 0,
	duplicateCount: 0,
	newFileUrl: ''
});

// 监听签名选择变化
watch(() => formValue.value.signId, (newSignId) => {
	currentSignText.value = signOption.value.find(item => item.value === newSignId)?.label || '';
});

// 计算总字数
function getTotalLength(): number {
	const content = formValue.value.content || '';
	return currentSignText.value.length + content.length;
}

function calculateFeeNum(content: string): number {
	if (!content) return 0;
	const length = currentSignText.value.length + content.length;
	// 一条短信70个字，超过70个字按67个字一条计费
	if (length <= 70) {
		return 1;
	}
	return Math.ceil(length / 67);
}

// 监听模板变化，更新短信内容
watch(() => formValue.value.templateId, (val) => {
	if (formValue.value.smsMode === 2) {
		formValue.value.content = templateOption.value.find((item) => item.value === val)?.content || '';
	}
});



// 监听短信模式变化，更新短信内容及模板选项
watch(() => formValue.value.smsMode, () => {
	// 重置所有状态
	formValue.value.content = '';
	formValue.value.templateId = null;

	// 调用handleRemove来清除上传的文件和相关状态
	handleRemove();
});

// 监听手动输入号码变化，实时更新总数量
watch(() => formValue.value.mobiles, () => {
	formValue.value.mobileNum = calculateTotalMobileCount();
});

// 下载模板excel
function downloadExcelFile() {
	if (formValue.value.templateId == null) {
		message.warning("请先选择短信模板")
		return
	}
	DownloadExcel({ id: formValue.value.templateId })
}

// 大文件分片上传
const dialog = useDialog();
const chunkSize = 2 * 1024 * 1024; // 每个分片大小限制，默认2M
const uploadStatus = ref(0); // 上传状态 0等待上传 1解析中 2上传中 3已取消
const progress = ref(0);
const sizeFormat = ref('0B');

// 取消上传
function handleRemove() {
	console.log('handleRemove')
	if (uploadStatus.value == 1 || uploadStatus.value == 2) {
		uploadStatus.value = 3;
		dialog.info({
			title: '提示',
			content: '已取消大文件上传，已上传的文件不会自动删除，重新操作可进行断点续传',
			positiveText: '确定',
		});
	}
	formValue.value.fileUrl = '';// 清空已上传文件
	formValue.value.attachmentId = 0; // 清空附件ID
	formValue.value.mobileNum = 0; // 重置号码数量
	// 重置检查结果
	checkResult.value = {
		successCount: 0,
		failCount: 0,
		duplicateCount: 0,
		newFileUrl: ''
	};
}

// 上传前检查
const beforeUpload = (data: { file: UploadFileInfo }) => {
	if (!formValue.value.templateId) {
		message.warning('请先选择短信模板');
		return false;
	}
	return true;
}

// 开始上传
function handleUpload(options: UploadCustomRequestOptions) {
	uploadStatus.value = 1;

	// 初始化上传进度
	updateProgress(options, 0);

	const file = options.file.file as File;
	const fileReader = new FileReader();
	fileReader.readAsArrayBuffer(file);
	fileReader.onload = async (e: ProgressEvent<FileReader>) => {
		if (!e.target) return;
		const spark = new SparkMD5.ArrayBuffer();
		spark.append(e.target.result);
		let md5 = spark.end();
		let start = 0;
		let end = 0;
		let index = 0;
		let shards: any[] = [];
		while (end < file.size) {
			start = index * chunkSize;
			end = (index + 1) * chunkSize;

			const params: UploadFileParams = {
				uploadType: 'doc',
				md5: md5,
				index: index + 1,
				fileName: file.name,
				file: file.slice(start, end),
			};

			const shard = { index: index + 1, params: params };
			shards.push(shard);
			index++;
		}

		uploadStatus.value = 2;

		const params = {
			uploadType: 'doc',
			fileName: file.name,
			size: file.size,
			md5: md5,
			shardCount: shards.length,
		};

		CheckMultipart(params)
			.then(async (res) => {
				// 已存在
				if (!res.waitUploadIndex || res.waitUploadIndex.length == 0) {
					onFinish(options, res.attachment);
					return;
				}

				// 断点续传，过滤掉已上传成功的分片文件
				shards = shards.filter((shard) => res.waitUploadIndex.includes(shard.index));
				if (shards.length == 0) {
					onFinish(options, res.attachment);
					return;
				}

				// 导入断点续传进度
				updateProgress(options, res.progress);
				sizeFormat.value = res.sizeFormat;

				for (const item of shards) {
					if (uploadStatus.value == 3) {
						break;
					}
					item.params.uploadId = res.uploadId;
					await handleUploadPart(options, item);
				}
			})
			.catch(() => {
				uploadStatus.value = 0;
				options.onError();
			});
	};
}

// 上传分片文件
async function handleUploadPart(options: UploadCustomRequestOptions, item: any) {
	const res = await UploadPart(item.params) as {
		progress: number;
		finish: boolean;
		attachment: Attachment;
	};
	updateProgress(options, res.progress);
	if (res.finish) {
		onFinish(options, res.attachment);
	}
}

// 更新上传进度
function updateProgress(options: UploadCustomRequestOptions, value: number) {
	options.onProgress({ percent: value });
	progress.value = value;
}

// 上传成功后的回调
async function onFinish(options: UploadCustomRequestOptions, result: Attachment) {
	formValue.value.fileUrl = result.fileUrl;
	formValue.value.attachmentId = result.id;

	// 调用文件检查接口
	try {
		console.log('检查文件前:', checkResult.value);
		const params = {
			attachmentId: result.id,
			fileUrl: result.fileUrl,
			...(formValue.value.smsMode === 2 ? { templateId: formValue.value.templateId } : {})
		};
		const res = await CheckFile(params);

		checkResult.value = {
			successCount: res.successCount,
			failCount: res.failCount,
			duplicateCount: res.duplicateCount,
			newFileUrl: res.newFileUrl
		};
		console.log('检查文件后:', checkResult.value);

		// 更新文件路径为新的路径
		formValue.value.fileUrl = res.newFileUrl;
		// 重新计算总的号码数量（包括文件、手动输入和通讯录分组）
		formValue.value.mobileNum = calculateTotalMobileCount();

		console.log('onFinish', result);
		options.onFinish();
		message.success('上传成功');
		uploadStatus.value = 0;
	} catch (error) {
		message.error('文件上传失败，请重新上传');
		console.error('文件检查失败:', error);
		// 清空相关状态
		// formValue.value.fileUrl = '';
		// formValue.value.attachmentId = 0;
		// formValue.value.mobileNum = 0;
		checkResult.value = {
			successCount: 0,
			failCount: 0,
			duplicateCount: 0,
			newFileUrl: ''
		};
		// 重置上传状态，允许重新上传
		uploadStatus.value = 0;
		progress.value = 0;
		sizeFormat.value = '0B';
		options.onError(); // 通知上传组件上传失败
	}
}


// 提交表单
async function confirmForm(e) {
	e.preventDefault();

	// 文件上传失败，阻止提交
	if (formValue.value.attachmentId > 0 && !checkResult.value.newFileUrl) {
		message.error('请先完成文件检查');
		return;
	}

	// 确保使用最新计算的号码数量
	formValue.value.mobileNum = calculateTotalMobileCount();

	const manualMobileCount = formValue.value.mobiles ? formValue.value.mobiles.split(',').filter(item => item.trim()).length : 0;
	const hasContactGroups = selectedGroups.value.length > 0;

	// 将选择的分组信息添加到提交数据中
	formValue.value.contactGroups = selectedGroups.value.length > 0 ? JSON.stringify(selectedGroups.value) : '';

	// 如果模式是常规，即没上传文件、没输入号码、也没选择分组，则阻止提交
	if (formValue.value.smsMode === 1 && checkResult.value.successCount === 0 && manualMobileCount === 0 && !hasContactGroups) {
		message.error('请上传文件、填写手机号码或选择通讯录分组');
		return;
	}

	formRef.value.validate((errors) => {
		if (!errors) {
			formBtnLoading.value = true;
			formValue.value.auditStatus = 1;// 编辑时将审核状态修改为待审核
			Edit(formValue.value)
				.then((_res) => {
					message.success('操作成功');
					closeForm();
					// emit('reloadTable');
				})
				.finally(() => {
					formBtnLoading.value = false;
				});
		} else {
			message.error('请填写完整信息');
		}
	});
}

// 关闭表单
function closeForm() {
	router.go(-1)
}

// 打开模态框
function openModal(state: State) {
	showModal.value = true;

	loadTemplateOption();
	loadSignOption();

	// 新增
	if (!state || state.id < 1) {
		formValue.value = newState(state);

		return;
	}

	// 编辑
	loading.value = true;
	View({ id: state.id })
		.then((res) => {
			formValue.value = res;
		})
		.finally(() => {
			loading.value = false;
		});
}

// 通讯录选择相关方法
function openContactBook() {
	showContactBook.value = true;
}

function handleContactBookSelect(groups) {
	selectedGroups.value = groups;
	updateMobilesFromSelection();
}

// 联系人选择相关方法
function openContactSelector() {
	showContactSelector.value = true;
}

function handleContactSelect(contacts) {
	// 提取选中联系人的手机号
	const mobiles = contacts.map(contact => contact.mobile).filter(mobile => mobile);

	// 获取当前手动输入的号码
	const currentMobiles = formValue.value.mobiles ? formValue.value.mobiles.split(',').filter(item => item.trim()) : [];

	// 合并号码并去重
	const allMobiles = [...new Set([...currentMobiles, ...mobiles])];

	// 更新到textarea
	formValue.value.mobiles = allMobiles.join(',');

	// 重新计算总数量
	formValue.value.mobileNum = calculateTotalMobileCount();
}

// 处理手机号码输入
function handleMobilesInput(value: string) {
	// 检查是否包含中文逗号
	if (value.includes('，')) {
		// 显示错误提示
		message.error('号码分隔请使用英文逗号，不能使用中文逗号');
	}
}

// 计算总的号码数量
function calculateTotalMobileCount() {
	const manualMobileCount = formValue.value.mobiles ? formValue.value.mobiles.split(',').filter(item => item.trim()).length : 0;
	const hasContactGroups = selectedGroups.value.length > 0;

	let contactGroupMobileCount = 0;
	if (hasContactGroups) {
		// 检查是否选择了"所有联系人"
		const hasAllContacts = selectedGroups.value.some(group => group.key === 'all');

		if (hasAllContacts) {
			// 如果选择了"所有联系人"，只计算"所有联系人"的数量
			const allGroup = selectedGroups.value.find(group => group.key === 'all');
			contactGroupMobileCount = allGroup?.count || 0;
		} else {
			// 否则计算所有选中分组的联系人总数
			contactGroupMobileCount = selectedGroups.value.reduce((total, group) => {
				return total + (group.count || 0);
			}, 0);
		}
	}

	return checkResult.value.successCount + manualMobileCount + contactGroupMobileCount;
}

// 根据选择的分组更新显示信息（不更新手机号码字段）
function updateMobilesFromSelection() {
	// 这里不再更新手机号码字段，因为我们只传递分组ID给后端
	// 手机号码字段只用于显示手动输入的号码

	// 更新总的号码数量
	formValue.value.mobileNum = calculateTotalMobileCount();
}

// 删除单个分组
function removeGroup(groupKey) {
	selectedGroups.value = selectedGroups.value.filter(group => group.key !== groupKey);
	updateMobilesFromSelection();
}

// 清空所有分组
function clearAllGroups() {
	selectedGroups.value = [];
	updateMobilesFromSelection();
}

onMounted(() => {
	loadTemplateOption();
	loadSignOption();
});
defineExpose({
	openModal,
});
</script>

<style lang="less">
.add {
	width: 600px;
	float: left;

	&_box {
		background: #fff;
		border-radius: 8px;
		overflow: hidden;
	}
}

.info {
	float: left;
	padding-top: 20px;
	padding-left: 100px;
	color: #f00;
}
</style>
