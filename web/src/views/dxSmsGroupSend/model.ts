import { h, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { FormSchema } from '@/components/Form';
import { defRangeShortcuts, formatToDate } from '@/utils/dateUtil';
import { MemberSumma, renderOptionTag, renderPopoverMemberSumma } from '@/utils';
import { useDictStore } from '@/store/modules/dict';
import { useUserStore } from '@/store/modules/user';
import { Option as TemplateOption } from '@/api/dxSmsGroupTemplate'
import { Option as SignOption } from '@/api/dxSmsSign'

const dict = useDictStore();
const userStore = useUserStore();

export class State {
  public id = 0; // ID
  public memberId = 0; // 用户ID
  public batchNo = ''; // 批次号
  public smsMode = 1; // 短信模式
  public sendMode = 1; // 发送模式
  public signId = null; // 签名
  public channelId = 0; // 通道
  public templateId = null; // 模板
  public content = ''; // 短信内容
  public mobileNum = 0; // 号码数量
  public remark = ''; // 备注
  public sendStatus = 0; // 发送状态
  public auditStatus = 0; // 审核状态
  public scheduleTime = ''; // 定时发送时间
  public cycleStartDate = ''; // 周期发送开始日期
  public cycleEndDate = ''; // 周期发送结束日期
  public cycleStartTime = ''; // 周期发送开始时间
  public cycleEndTime = ''; // 周期发送结束时间
  public cycleFreq = 0; // 周期发送频次/天
  public createdAt = ''; // 创建时间
  public deletedAt = ''; // 删除时间
  public memberBySumma?: null | MemberSumma = null; // 创建人摘要信息
  public dxSmsSignSignText = ''; // 短信签名
  public dxSmsGroupTemplateTplName = ''; // 模板名称
  public attachmentId = 0; // 附件ID
  public fileUrl = ''; // 文件地址
  public mobiles = ''; // 手机号列表
  public contactGroups = ''; // 通讯录分组（JSON格式）
  public sysSmsChannelChannelName = ''; // 通道名称

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则
export const rules = {
  // memberId: {
  //   required: true,
  //   trigger: ['blur', 'input'],
  //   type: 'number',
  //   message: '请输入用户ID',
  // },
  // batchNo: {
  //   required: true,
  //   trigger: ['blur', 'input'],
  //   type: 'string',
  //   message: '请输入批次号',
  // },
  smsMode: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: '请输入短信模式',
  },
  sendMode: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: '请输入发送模式',
  },
  signId: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: '请输入签名',
  },
  // channelId: {
  //   required: true,
  //   trigger: ['blur', 'input'],
  //   type: 'number',
  //   message: '请输入通道',
  // },
  // templateId: {
  //   required: true,
  //   trigger: ['blur', 'input'],
  //   type: 'number',
  //   message: '请输入模板',
  // },
  content: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: '请输入短信内容',
  },
  mobiles: {
    trigger: ['blur', 'input'],
    validator: (rule: any, value: string) => {
      if (!value) return true; // 允许为空，因为可以通过其他方式选择号码

      // 检查是否包含中文逗号
      if (value.includes('，')) {
        return new Error('号码分隔请使用英文逗号，不能使用中文逗号');
      }

      return true;
    },
  },
  // mobileNum: {
  //   required: true,
  //   trigger: ['blur', 'input'],
  //   type: 'number',
  //   message: '请输入号码数量',
  // },
  // sendStatus: {
  //   required: true,
  //   trigger: ['blur', 'input'],
  //   type: 'number',
  //   message: '请输入发送状态',
  // },
  // auditStatus: {
  //   required: true,
  //   trigger: ['blur', 'input'],
  //   type: 'number',
  //   message: '请输入审核状态',
  // },
};

// 表格搜索表单
export const schemas = ref<FormSchema[]>([
  {
    field: 'adminMemberRealName',
    component: 'NInput',
    label: '企业名称',
    componentProps: {
      placeholder: '请输入姓名',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    ifShow: () => {
      return userStore.isCompanyDept
    },
  },
  {
    field: 'adminMemberUserName',
    component: 'NInput',
    label: '子账号',
    componentProps: {
      placeholder: '请输入子账号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    ifShow: () => {
      return !userStore.isUserDept
    },
  },
  {
    field: 'batchNo',
    component: 'NInput',
    label: '批次号',
    componentProps: {
      placeholder: '请输入批次号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  // {
  //   field: 'dxSmsSignSignText',
  //   component: 'NInput',
  //   label: '签名',
  //   componentProps: {
  //     placeholder: '请输入签名',
  //     onUpdateValue: (e: any) => {
  //       console.log(e);
  //     },
  //   },
  // },
  {
    field: 'smsMode',
    component: 'NSelect',
    label: '短信模式',
    defaultValue: null,
    componentProps: {
      options: dict.getOption('sms_mode'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'dxSmsGroupTemplateTplName',
    component: 'NInput',
    label: '模板名称',
    componentProps: {
      placeholder: '请输入模板名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  // {
  //   field: 'sendMode',
  //   component: 'NSelect',
  //   label: '发送模式',
  //   defaultValue: null,
  //   componentProps: {
  //     placeholder: '请选择发送模式',
  //     options: dict.getOption('send_mode'),
  //     onUpdateValue: (e: any) => {
  //       console.log(e);
  //     },
  //   },
  // },
  {
    field: 'sendStatus',
    component: 'NSelect',
    label: '发送状态',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择发送状态',
      options: dict.getOption('send_status'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'auditStatus',
    component: 'NSelect',
    label: '审核状态',
    defaultValue: null,
    componentProps: {
      options: dict.getOption('audit_status'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'createdAt',
    component: 'NDatePicker',
    label: '创建时间',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      shortcuts: defRangeShortcuts(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
]);

// 表格列
export const columns = [
  // {
  //   title: 'ID',
  //   key: 'id',
  //   align: 'left',
  //   width: 60,
  // },
  {
    title: '企业名称',
    key: 'memberBySumma',
    align: 'left',
    width: -1,
    render(row: State) {
      return row.memberBySumma?.realName;
    },
    ifShow: () => {
      return userStore.isCompanyDept
    },
  },
  {
    title: '子账号',
    key: 'batchNo',
    align: 'left',
    width: 120,
    render(row: State) {
      return row.memberBySumma?.username;
    },
  },
  {
    title: '批次号',
    key: 'batchNo',
    align: 'left',
    width: 180,
  },
  // {
  //   title: '企业名称',
  //   key: 'memberBySumma',
  //   align: 'left',
  //   width: -1,
  //   render(row: State) {
  //     // return renderPopoverMemberSumma(row.memberBySumma);
  //     return row.memberBySumma?.realName;
  //   },
  //   ifShow: () => {
  //     return userStore.isCompanyDept
  //   },
  // },
  // {
  //   title: '子账号',
  //   key: 'memberBySumma',
  //   align: 'left',
  //   width: -1,
  //   render(row: State) {
  //     // return renderPopoverMemberSumma(row.memberBySumma);
  //     return row.memberBySumma?.username;
  //   },
  //   ifShow: () => {
  //     return !userStore.isUserDept
  //   },
  // },
  {
    title: '类型',
    key: 'smsMode',
    align: 'left',
    width: 80,
    render(row: State) {
      return renderOptionTag('sms_mode', row.smsMode);
    },
  },
  // {
  //   title: '模板名称',
  //   key: 'dxSmsGroupTemplateTplName',
  //   align: 'left',
  //   width: -1,
  //   ifsShow: (state: State) => {
  //     return state.smsMode === 2;
  //   },
  // },
  {
    title: '短信内容',
    key: 'content',
    align: 'left',
    width: 250,
    render(row: State) {
      return h(
        'div',
        {
          style: {
            cursor: 'pointer',
            color: '#2d8cf0',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: '300px'
          },
          onClick: () => {
            const event = new CustomEvent('showSmsContent', { detail: row.dxSmsSignSignText + row.content });
            window.dispatchEvent(event);
          },
        },
        row.dxSmsSignSignText + row.content
      );
    },
    
  },
  {
    title: '号码数量',
    key: 'mobileNum',
    align: 'left',
    width: 100,
  },
  {
    title: '计费条数',
    key: 'feeNumCalc',
    align: 'left',
    width: -1,
    render(row: State) {
      // 类型为模板时显示--，否则按签名+内容长度计费
      // 假设smsType==2为模板短信
      if (row.smsMode === 2) {
        return '--';
      }
      // 计费公式：一条短信70字，超70后每67字一条
      const signLen = row.dxSmsSignSignText ? row.dxSmsSignSignText.length : 0;
      const contentLen = row.content ? row.content.length : 0;
      const totalLen = signLen + contentLen;
      if (totalLen === 0) return 0;
      if (totalLen <= 70) return 1;
      return Math.ceil(totalLen / 67);
    },
  },
  {
    title: '发送模式',
    key: 'sendMode',
    align: 'left',
    width: 120,
    render(row: State) {
      return renderOptionTag('send_mode', row.sendMode);
    },
  },
  {
    title: '创建时间',
    key: 'createdAt',
    align: 'left',
    width: 170,
  },
  {
    title: '定时时间',
    key: 'scheduleTime',
    align: 'left',
    width: 170,
  },
  // {
  //   title: '签名',
  //   key: 'dxSmsSignSignText',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '备注',
  //   key: 'remark',
  //   align: 'left',
  //   width: -1,
  // },
  {
    title: '审核状态',
    key: 'auditStatus',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('audit_status', row.auditStatus);
    },
  },
  {
    title: '发送状态',
    key: 'sendStatus',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('send_status', row.sendStatus);
    },
  },
  // {
  //   title: '定时发送时间',
  //   key: 'scheduleTime',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '周期发送开始日期',
  //   key: 'cycleStartDate',
  //   align: 'left',
  //   width: -1,
  //   render(row: State) {
  //     return formatToDate(row.cycleStartDate);
  //   },
  // },
  // {
  //   title: '周期发送结束日期',
  //   key: 'cycleEndDate',
  //   align: 'left',
  //   width: -1,
  //   render(row: State) {
  //     return formatToDate(row.cycleEndDate);
  //   },
  // },
  // {
  //   title: '周期发送开始时间',
  //   key: 'cycleStartTime',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '周期发送结束时间',
  //   key: 'cycleEndTime',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '周期发送频次/天',
  //   key: 'cycleFreq',
  //   align: 'left',
  //   width: -1,
  // },
];

// 表格列生成函数，支持点击处理
export function getColumns(onCompanyNameClick?: (record: State) => void, onSubAccountClick?: (record: State) => void, onBatchNoClick?: (record: State) => void) {
  return [
    // {
    //   title: 'ID',
    //   key: 'id',
    //   align: 'left',
    //   width: 60,
    // },
    {
      title: '企业名称',
      key: 'memberBySumma',
      align: 'left',
      width: -1,
      render(row: State) {
        const realName = row.memberBySumma?.realName;
        if (onCompanyNameClick && realName) {
          return h('span', {
            style: { cursor: 'pointer', color: '#2d8cf0', textDecoration: 'underline' },
            onClick: () => onCompanyNameClick(row)
          }, realName);
        }
        return realName;
      },
      ifShow: () => {
        return userStore.isCompanyDept
      },
    },
    {
      title: '子账号',
      key: 'subAccount',
      align: 'left',
      width: 120,
      render(row: State) {
        const username = row.memberBySumma?.username;
        if (onSubAccountClick && username) {
          return h('span', {
            style: { cursor: 'pointer', color: '#2d8cf0', textDecoration: 'underline' },
            onClick: () => onSubAccountClick(row)
          }, username);
        }
        return username;
      },
    },
    {
      title: '批次号',
      key: 'batchNo',
      align: 'left',
      width: 180,
      render(row: State) {
        if (onBatchNoClick && row.batchNo) {
          return h('span', {
            style: { cursor: 'pointer', color: '#2d8cf0', textDecoration: 'underline' },
            onClick: () => onBatchNoClick(row)
          }, row.batchNo);
        }
        return row.batchNo;
      },
    },
    {
      title: '类型',
      key: 'smsMode',
      align: 'left',
      width: 80,
      render(row: State) {
        return renderOptionTag('sms_mode', row.smsMode);
      },
    },
    {
      title: '短信内容',
      key: 'content',
      align: 'left',
      width: 250,
      render(row: State) {
        return h(
          'div',
          {
            style: {
              cursor: 'pointer',
              color: '#2d8cf0',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: '300px'
            },
            onClick: () => {
              const event = new CustomEvent('showSmsContent', { detail: row.dxSmsSignSignText + row.content });
              window.dispatchEvent(event);
            },
          },
          row.dxSmsSignSignText + row.content
        );
      },
    },
    {
      title: '号码数量',
      key: 'mobileNum',
      align: 'left',
      width: 100,
    },
    {
      title: '计费条数',
      key: 'feeNumCalc',
      align: 'left',
      width: -1,
      render(row: State) {
        // 类型为模板时显示--，否则按签名+内容长度计费
        // 假设smsType==2为模板短信
        if (row.smsMode === 2) {
          return '--';
        }
        // 计费公式：一条短信70字，超70后每67字一条
        const signLen = row.dxSmsSignSignText ? row.dxSmsSignSignText.length : 0;
        const contentLen = row.content ? row.content.length : 0;
        const totalLen = signLen + contentLen;
        if (totalLen === 0) return 0;
        if (totalLen <= 70) return 1;
        return Math.ceil(totalLen / 67);
      },
    },
    {
      title: '发送模式',
      key: 'sendMode',
      align: 'left',
      width: 120,
      render(row: State) {
        return renderOptionTag('send_mode', row.sendMode);
      },
    },
    {
      title: '创建时间',
      key: 'createdAt',
      align: 'left',
      width: 170,
    },
    {
      title: '定时时间',
      key: 'scheduleTime',
      align: 'left',
      width: 170,
    },
    {
      title: '审核状态',
      key: 'auditStatus',
      align: 'left',
      width: -1,
      render(row: State) {
        return renderOptionTag('audit_status', row.auditStatus);
      },
    },
    {
      title: '发送状态',
      key: 'sendStatus',
      align: 'left',
      width: -1,
      render(row: State) {
        return renderOptionTag('send_status', row.sendStatus);
      },
    },
  ];
}

// 加载字典数据选项
export function loadOptions() {
  dict.loadOptions(['audit_status', 'sms_type', 'send_mode', 'send_status', 'sms_mode']);
}

// 短信模板选项
export const templateOption = ref<{ key: string | number, value: string | number, content: string }[]>([]);
// 短信签名选项
export const signOption = ref<{ key: string | number, value: string | number }[]>([]);

// 加载短信模板选项
export function loadTemplateOption() {
  TemplateOption().then((res) => {
    templateOption.value = res.list;
  });
}

// 加载短信签名选项
export function loadSignOption() {
  SignOption().then((res) => {
    signOption.value = res.list;
  });
}
