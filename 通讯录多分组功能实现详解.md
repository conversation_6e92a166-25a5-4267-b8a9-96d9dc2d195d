# 通讯录多分组功能实现详解

## 🎯 核心改进

支持一个联系人属于多个分组，采用标准的多对多关系设计。

## 📊 数据库设计

### 表结构调整

1. **联系人表** (`hg_contact`)
   - 移除 `group_id` 和 `group_name` 字段
   - 专注于联系人基本信息

2. **分组表** (`hg_contact_group`)
   - 保持不变，提供分组基础信息

3. **关系表** (`hg_contact_group_relation`) - **新增**
   - 实现联系人与分组的多对多关系
   - 包含冗余的 `member_id` 提高查询性能

## 🔧 核心功能实现

### 1. 联系人分组管理API

```go
// 更新联系人的分组关系
func (c *cContact) UpdateGroups(ctx context.Context, req *contact.UpdateGroupsReq) (res *contact.UpdateGroupsRes, err error) {
    memberId := service.Context().GetUserId(ctx)
    
    // 事务处理
    return service.Contact().UpdateContactGroups(ctx, req.ContactId, req.GroupIds, memberId)
}
```

### 2. 分组筛选查询

```go
// 按分组筛选联系人
func (c *cContact) ListByGroup(ctx context.Context, req *contact.ListByGroupReq) (res *contact.ListByGroupRes, err error) {
    memberId := service.Context().GetUserId(ctx)
    
    query := dao.Contact.Ctx(ctx).Where("member_id", memberId)
    
    switch req.GroupId {
    case 0: // 所有联系人
        break
    case -1: // 未分组联系人
        query = query.LeftJoin("hg_contact_group_relation cgr", "hg_contact.id = cgr.contact_id").
            Where("cgr.contact_id IS NULL")
    default: // 指定分组
        query = query.InnerJoin("hg_contact_group_relation cgr", "hg_contact.id = cgr.contact_id").
            Where("cgr.group_id", req.GroupId)
    }
    
    // 分页查询
    contacts, err := query.Page(req.Page, req.PageSize).All()
    if err != nil {
        return
    }
    
    // 为每个联系人加载分组信息
    for i, contact := range contacts {
        groups, _ := dao.ContactGroupRelation.Ctx(ctx).
            LeftJoin("hg_contact_group cg", "hg_contact_group_relation.group_id = cg.id").
            Where("contact_id", contact.Id).
            Fields("cg.id, cg.name").
            All()
        contacts[i].Groups = groups
    }
    
    return &contact.ListByGroupRes{
        List: contacts,
        Total: total,
    }, nil
}
```

### 3. 分组统计功能

```go
// 获取分组统计信息
func (c *cContact) GroupStats(ctx context.Context, req *contact.GroupStatsReq) (res *contact.GroupStatsRes, err error) {
    memberId := service.Context().GetUserId(ctx)
    
    // 查询所有分组
    groups, err := dao.ContactGroup.Ctx(ctx).
        Where("member_id", memberId).
        OrderAsc("sort").
        All()
    if err != nil {
        return
    }
    
    // 统计各分组的联系人数量
    var groupStats []map[string]interface{}
    for _, group := range groups {
        count, _ := dao.ContactGroupRelation.Ctx(ctx).
            Where(g.Map{
                "group_id": group.Id,
                "member_id": memberId,
            }).Count()
        
        groupStats = append(groupStats, map[string]interface{}{
            "id": group.Id,
            "name": group.Name,
            "count": count,
        })
    }
    
    // 统计总数和未分组数
    total, _ := dao.Contact.Ctx(ctx).Where("member_id", memberId).Count()
    ungrouped, _ := dao.Contact.Ctx(ctx).
        Where("member_id", memberId).
        LeftJoin("hg_contact_group_relation cgr", "hg_contact.id = cgr.contact_id").
        Where("cgr.contact_id IS NULL").
        Count()
    
    return &contact.GroupStatsRes{
        Groups: groupStats,
        Total: total,
        Ungrouped: ungrouped,
    }, nil
}
```

## 🎨 前端实现

### 1. 联系人编辑表单

```vue
<template>
  <a-form-item label="所属分组" name="groupIds">
    <a-checkbox-group v-model:value="formData.groupIds">
      <a-checkbox 
        v-for="group in groupOptions" 
        :key="group.id" 
        :value="group.id"
      >
        {{ group.name }}
      </a-checkbox>
    </a-checkbox-group>
  </a-form-item>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        groupIds: [] // 支持多选
      },
      groupOptions: [] // 分组选项
    }
  },
  
  methods: {
    // 保存时调用分组更新API
    async saveContact() {
      // 先保存联系人基本信息
      const contact = await this.$api.contact.save(this.formData)
      
      // 更新分组关系
      await this.$api.contact.updateGroups({
        contactId: contact.id,
        groupIds: this.formData.groupIds
      })
    }
  }
}
</script>
```

### 2. 联系人列表显示

```vue
<template>
  <a-table :columns="columns" :data-source="contactList">
    <template #groups="{ record }">
      <a-tag 
        v-for="group in record.groups" 
        :key="group.id"
        color="blue"
      >
        {{ group.name }}
      </a-tag>
    </template>
  </a-table>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { title: '姓名', dataIndex: 'name' },
        { title: '手机', dataIndex: 'mobile' },
        { title: '所属分组', dataIndex: 'groups', scopedSlots: { customRender: 'groups' } }
      ]
    }
  }
}
</script>
```

## 🚀 Service层实现

```go
// service/contact.go
func (s *sContact) UpdateContactGroups(ctx context.Context, contactId int64, groupIds []int64, memberId int64) error {
    return dao.ContactGroupRelation.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        // 删除原有关系
        _, err := dao.ContactGroupRelation.Ctx(ctx).TX(tx).
            Where(g.Map{
                "contact_id": contactId,
                "member_id": memberId,
            }).Delete()
        if err != nil {
            return err
        }
        
        // 添加新关系
        for _, groupId := range groupIds {
            _, err = dao.ContactGroupRelation.Ctx(ctx).TX(tx).Insert(g.Map{
                "contact_id": contactId,
                "group_id": groupId,
                "member_id": memberId,
                "created_by": memberId,
                "created_at": gtime.Now(),
            })
            if err != nil {
                return err
            }
        }
        
        return nil
    })
}

// 获取联系人的分组信息
func (s *sContact) GetContactGroups(ctx context.Context, contactId int64) ([]map[string]interface{}, error) {
    return dao.ContactGroupRelation.Ctx(ctx).
        LeftJoin("hg_contact_group cg", "hg_contact_group_relation.group_id = cg.id").
        Where("contact_id", contactId).
        Fields("cg.id, cg.name").
        All()
}
```

## 📝 Input/Output 模型

```go
// input models
type UpdateGroupsReq struct {
    ContactId int64   `json:"contactId" v:"required#联系人ID不能为空"`
    GroupIds  []int64 `json:"groupIds" v:"required#分组ID不能为空"`
}

type ListByGroupReq struct {
    GroupId  int64 `json:"groupId" v:"required#分组ID不能为空"`
    Page     int   `json:"page" v:"min:1#页码不能小于1"`
    PageSize int   `json:"pageSize" v:"min:1|max:100#每页数量不能小于1或大于100"`
}

// output models
type ContactWithGroups struct {
    *entity.Contact
    Groups []map[string]interface{} `json:"groups"`
}

type ListByGroupRes struct {
    List     []ContactWithGroups `json:"list"`
    Total    int                 `json:"total"`
    Page     int                 `json:"page"`
    PageSize int                 `json:"pageSize"`
}
```

## 🔍 查询优化

### 1. 索引优化
```sql
-- 关系表索引
CREATE INDEX idx_contact_member ON hg_contact_group_relation(contact_id, member_id);
CREATE INDEX idx_group_member ON hg_contact_group_relation(group_id, member_id);

-- 联合索引提高查询性能
CREATE INDEX idx_member_group_contact ON hg_contact_group_relation(member_id, group_id, contact_id);
```

### 2. 批量查询优化
```go
// 批量获取联系人分组信息
func (s *sContact) BatchGetContactGroups(ctx context.Context, contactIds []int64) (map[int64][]map[string]interface{}, error) {
    relations, err := dao.ContactGroupRelation.Ctx(ctx).
        LeftJoin("hg_contact_group cg", "hg_contact_group_relation.group_id = cg.id").
        WhereIn("contact_id", contactIds).
        Fields("contact_id, cg.id, cg.name").
        All()
    
    if err != nil {
        return nil, err
    }
    
    // 组织返回数据
    result := make(map[int64][]map[string]interface{})
    for _, relation := range relations {
        contactId := relation["contact_id"].Int64()
        if result[contactId] == nil {
            result[contactId] = make([]map[string]interface{}, 0)
        }
        result[contactId] = append(result[contactId], map[string]interface{}{
            "id":   relation["id"],
            "name": relation["name"],
        })
    }
    
    return result, nil
}
```

## 🎯 主要优势

1. **数据结构标准化**：符合关系数据库设计范式
2. **功能灵活性**：支持复杂的分组关系管理
3. **查询性能**：通过索引和批量查询优化性能
4. **扩展性好**：可以轻松添加更多分组相关功能
5. **数据一致性**：事务处理确保数据完整性

## 📋 实施注意事项

1. **数据迁移**：从旧的单分组设计迁移到多分组需要数据转换
2. **权限验证**：确保用户只能操作自己的联系人和分组
3. **性能监控**：关注复杂查询的性能表现
4. **UI/UX**：多分组的界面设计需要考虑用户体验

这种设计为通讯录提供了更强大和灵活的分组功能！ 