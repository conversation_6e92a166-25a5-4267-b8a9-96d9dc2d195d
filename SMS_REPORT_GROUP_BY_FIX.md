# 短信报表按月统计GROUP BY错误修复

## 问题描述

在按月统计功能中，MySQL返回以下错误：

```
Error 1055 (42000): Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'dxpt.hg_dx_sms_send_daily_stats.stat_date' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by
```

## 问题分析

### 1. MySQL sql_mode 限制
MySQL的 `sql_mode=only_full_group_by` 模式要求：
- SELECT列表中的非聚合列必须出现在GROUP BY子句中
- 或者这些列在功能上依赖于GROUP BY中的列

### 2. 具体问题
**原有代码中的问题**：
```sql
-- SELECT中使用的表达式
DATE_FORMAT(hg_dx_sms_send_daily_stats.stat_date, '%Y-%m-01') as stat_date

-- GROUP BY中使用的表达式  
GROUP BY DATE_FORMAT(hg_dx_sms_send_daily_stats.stat_date, '%Y-%m')
```

**问题分析**：
- SELECT中的格式：`'%Y-%m-01'` (例如：2025-01-01)
- GROUP BY中的格式：`'%Y-%m'` (例如：2025-01)
- MySQL认为这是两个不同的表达式，不满足 `only_full_group_by` 要求

## 解决方案

### 1. 统一日期表达式
创建一个统一的日期格式表达式，在SELECT和GROUP BY中使用相同的表达式：

```go
// 定义统一的日期格式表达式
dateFormatExpr := "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m')"
```

### 2. 修改SELECT字段
使用CONCAT函数在统一的日期表达式基础上添加后缀：

```go
selectFields := []string{
    "CONCAT(" + dateFormatExpr + ", '-01') as stat_date", // 使用CONCAT添加-01后缀
    // ... 其他字段
}
```

### 3. 修改GROUP BY字段
使用相同的日期表达式：

```go
groupFields := []string{
    dateFormatExpr, // 使用相同的日期格式表达式
    dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().MemberId,
    dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().SmsType,
}
```

## 修复前后对比

### 修复前的SQL
```sql
SELECT 
    DATE_FORMAT(hg_dx_sms_send_daily_stats.stat_date, '%Y-%m-01') as stat_date,
    -- ... 其他字段
FROM hg_dx_sms_send_daily_stats
GROUP BY 
    DATE_FORMAT(hg_dx_sms_send_daily_stats.stat_date, '%Y-%m'),
    -- ... 其他字段
```

### 修复后的SQL
```sql
SELECT 
    CONCAT(DATE_FORMAT(hg_dx_sms_send_daily_stats.stat_date, '%Y-%m'), '-01') as stat_date,
    -- ... 其他字段
FROM hg_dx_sms_send_daily_stats
GROUP BY 
    DATE_FORMAT(hg_dx_sms_send_daily_stats.stat_date, '%Y-%m'),
    -- ... 其他字段
```

## 技术原理

### 1. 函数依赖性
在修复后的方案中：
- GROUP BY使用：`DATE_FORMAT(stat_date, '%Y-%m')`
- SELECT使用：`CONCAT(DATE_FORMAT(stat_date, '%Y-%m'), '-01')`
- CONCAT的结果在功能上依赖于GROUP BY中的表达式

### 2. MySQL认知
MySQL能够识别出CONCAT函数的参数与GROUP BY中的表达式相同，因此认为这是合法的。

### 3. 数据一致性
- 逻辑上仍然是按月分组
- 显示格式仍然是 `YYYY-MM-01` 的形式
- 满足前端日期识别的需求

## 代码实现细节

### 1. 变量定义
```go
// 定义统一的日期格式表达式，确保SELECT和GROUP BY使用相同的表达式
dateFormatExpr := "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m')"
```

### 2. SELECT字段构建
```go
selectFields := []string{
    "CONCAT(" + dateFormatExpr + ", '-01') as stat_date", // 基于统一表达式构建
    // ... 其他聚合字段
}
```

### 3. GROUP BY字段构建
```go
groupFields := []string{
    dateFormatExpr, // 直接使用统一的表达式
    // ... 其他分组字段
}
```

## 兼容性说明

### 1. MySQL版本兼容性
- 适用于MySQL 5.7及以上版本
- 兼容 `sql_mode=only_full_group_by` 模式
- 不影响其他SQL模式的正常运行

### 2. 数据格式兼容性
- 输出的日期格式保持不变：`YYYY-MM-01`
- 前端日期识别逻辑无需修改
- 与按天统计的数据格式保持一致

### 3. 性能影响
- CONCAT函数的性能开销极小
- 不影响索引使用
- 查询性能基本无变化

## 测试验证

### 1. 功能测试
- ✅ 按月统计查询正常执行
- ✅ 数据聚合结果正确
- ✅ 日期格式显示正确

### 2. 兼容性测试
- ✅ 不同MySQL版本兼容
- ✅ 不同sql_mode设置兼容
- ✅ 前端显示正常

### 3. 性能测试
- ✅ 查询性能无明显影响
- ✅ 大数据量下表现稳定

## 最佳实践总结

### 1. GROUP BY规范
在使用GROUP BY时，确保：
- SELECT中的非聚合列必须出现在GROUP BY中
- 或者在功能上依赖于GROUP BY中的列
- 使用相同的表达式避免歧义

### 2. 日期处理规范
在处理日期聚合时：
- 定义统一的日期格式表达式
- 在需要不同格式时使用函数转换
- 保持SELECT和GROUP BY的一致性

### 3. 代码维护性
- 使用变量存储复杂的表达式
- 添加注释说明设计意图
- 考虑不同数据库的兼容性

## 后续优化建议

1. **性能优化**：可以考虑在数据库层面添加计算列或视图
2. **代码重构**：可以将日期格式表达式提取为公共方法
3. **监控告警**：添加SQL执行监控，及时发现类似问题

## 总结

通过统一SELECT和GROUP BY中的日期表达式，成功解决了MySQL `only_full_group_by` 模式下的兼容性问题。修复后的代码既满足了数据库的严格要求，又保持了功能的完整性和数据格式的一致性。
