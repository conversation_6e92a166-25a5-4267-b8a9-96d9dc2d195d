# 通讯录模块最终方案

## 🎯 设计思路

**适配HotGo代码生成器的简化方案**：
- 主要使用代码生成器为联系人表生成标准CRUD功能
- 分组作为辅助功能，提供筛选和分类
- 在生成的基础上，手动添加左侧分组树筛选

## 📊 表结构优化

### 主要改进
1. **多对多关系设计** - 一个联系人可以属于多个分组
2. **关系表优化** - 添加冗余的member_id字段提高查询性能
3. **分组表简洁设计** - 基本的树形结构字段 + 描述字段（id, pid, name, description）

### 关键设计
```sql
-- 联系人表（主表）
hg_contact:
- 专注联系人基本信息（姓名、手机、公司、职位、部门、邮箱、备注）
- 不包含分组信息，通过关系表关联

-- 分组表（辅助表）
hg_contact_group:
- 简洁的树形结构（id, pid, name, description）
- 支持基本的分组分类功能

-- 联系人分组关系表（多对多）
hg_contact_group_relation:
- contact_id: 联系人ID
- group_id: 分组ID  
- member_id: 用户ID（冗余字段，提高查询性能）
- 唯一索引防止重复关系
```

## 🚀 实施步骤

### 第一步：更新数据库
```sql
-- 1. 删除旧表
DROP TABLE IF EXISTS hg_contact, hg_contact_group, hg_contact_group_relation;

-- 2. 执行新的建表SQL
source server/storage/data/contact_tables_optimized.sql
```

### 第二步：使用代码生成器
1. 登录后台 → 开发工具 → 代码生成
2. **选择 `hg_contact` 表**
   - 生成类型：**普通表格**
   - 模块名：`contact`
   - 功能名：`通讯录联系人`
   - 包路径：`admin/contact`
3. 配置字段属性：
   ```
   member_id: 隐藏字段（自动设置为当前用户）
   name: 必填
   mobile: 手机号验证
   email: 邮箱验证
   company: 文本输入
   position: 文本输入
   department: 文本输入
   remark: 文本域
   ```
4. 点击"提交生成"

### 第三步：生成分组管理功能
对 `hg_contact_group` 表：
- 生成类型：**树形表格**
- 模块名：`contactgroup`
- 功能名：`通讯录分组`

### 第四步：手动实现关系管理
`hg_contact_group_relation` 表需要手动编写代码：
- 联系人编辑时的分组选择功能
- 分组筛选时的查询逻辑
- 批量分组操作

### 第五步：前端页面定制
在生成的联系人管理页面基础上，添加左侧分组筛选：

```vue
<template>
  <div class="contact-page">
    <!-- 左侧分组筛选 -->
    <div class="contact-sidebar">
      <div class="group-list">
        <div class="group-item" 
             :class="{active: selectedGroupId === 0}"
             @click="selectGroup(0)">
          📁 所有联系人 ({{totalCount}})
        </div>
        <div class="group-item"
             :class="{active: selectedGroupId === -1}" 
             @click="selectGroup(-1)">
          📁 未分组 ({{ungroupedCount}})
        </div>
                 <div v-for="group in groupList" 
              :key="group.id"
              class="group-item"
              :class="{active: selectedGroupId === group.id}"
              @click="selectGroup(group.id)">
           📁 {{group.name}} ({{group.count}})
         </div>
      </div>
    </div>
    
    <!-- 右侧联系人列表（使用生成的组件） -->
    <div class="contact-main">
      <ContactList 
        :groupId="selectedGroupId"
        @refresh="loadGroupStats"
      />
    </div>
  </div>
</template>

<script>
import ContactList from './components/ContactList.vue' // 生成的组件

export default {
  name: 'ContactPage',
  components: { ContactList },
  data() {
    return {
      selectedGroupId: 0, // 当前选中的分组
      groupList: [],      // 分组列表
      totalCount: 0,      // 总联系人数
      ungroupedCount: 0   // 未分组联系人数
    }
  },
  
  async created() {
    await this.loadGroupStats()
  },
  
  methods: {
    // 选择分组
    selectGroup(groupId) {
      this.selectedGroupId = groupId
    },
    
    // 加载分组统计
    async loadGroupStats() {
      // 调用分组统计API
      const res = await this.$api.contact.groupStats()
      this.groupList = res.groups
      this.totalCount = res.total
      this.ungroupedCount = res.ungrouped
    }
  }
}
</script>

<style scoped>
.contact-page {
  display: flex;
  height: 100%;
}

.contact-sidebar {
  width: 260px;
  background: white;
  border-right: 1px solid #e0e0e0;
  padding: 16px;
}

.group-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 4px;
}

.group-item:hover {
  background: #f5f5f5;
}

.group-item.active {
  background: #1890ff;
  color: white;
}

.contact-main {
  flex: 1;
}
</style>
```

### 第六步：添加分组统计和筛选API（在生成的Controller基础上）
```go
// 在生成的contact controller中添加分组统计API
func (c *cContact) GroupStats(ctx context.Context, req *contact.GroupStatsReq) (res *contact.GroupStatsRes, err error) {
    memberId := service.Context().GetUserId(ctx)
    
    // 查询分组列表
    groups, err := dao.ContactGroup.Ctx(ctx).Where("member_id", memberId).OrderAsc("sort").All()
    if err != nil {
        return
    }
    
    // 统计每个分组的联系人数量
    var groupStats []map[string]interface{}
    for _, group := range groups {
        count, _ := dao.ContactGroupRelation.Ctx(ctx).Where(g.Map{
            "member_id": memberId,
            "group_id": group.Id,
        }).Count()
        
        groupStats = append(groupStats, map[string]interface{}{
            "id": group.Id,
            "name": group.Name,
            "pid": group.Pid,
            "count": count,
        })
    }
    
    // 统计总数和未分组数
    total, _ := dao.Contact.Ctx(ctx).Where("member_id", memberId).Count()
    ungrouped, _ := dao.Contact.Ctx(ctx).Where("member_id", memberId).
        LeftJoin("hg_contact_group_relation cgr", "hg_contact.id = cgr.contact_id").
        Where("cgr.contact_id IS NULL").Count()
    
    return &contact.GroupStatsRes{
        Groups: groupStats,
        Total: total,
        Ungrouped: ungrouped,
    }, nil
}

// 按分组筛选联系人API
func (c *cContact) ListByGroup(ctx context.Context, req *contact.ListByGroupReq) (res *contact.ListByGroupRes, err error) {
    memberId := service.Context().GetUserId(ctx)
    
    query := dao.Contact.Ctx(ctx).Where("member_id", memberId)
    
    switch req.GroupId {
    case 0: // 所有联系人
        // 不添加额外条件
    case -1: // 未分组联系人
        query = query.LeftJoin("hg_contact_group_relation cgr", "hg_contact.id = cgr.contact_id").
            Where("cgr.contact_id IS NULL")
    default: // 指定分组
        query = query.InnerJoin("hg_contact_group_relation cgr", "hg_contact.id = cgr.contact_id").
            Where("cgr.group_id", req.GroupId)
    }
    
    // 执行查询
    contacts, err := query.Page(req.Page, req.PageSize).All()
    if err != nil {
        return
    }
    
    total, err := query.Count()
    if err != nil {
        return
    }
    
    return &contact.ListByGroupRes{
        List: contacts,
        Total: total,
        Page: req.Page,
        PageSize: req.PageSize,
    }, nil
}

// 联系人分组管理API
func (c *cContact) UpdateGroups(ctx context.Context, req *contact.UpdateGroupsReq) (res *contact.UpdateGroupsRes, err error) {
    memberId := service.Context().GetUserId(ctx)
    
    // 删除原有关系
    _, err = dao.ContactGroupRelation.Ctx(ctx).Where(g.Map{
        "contact_id": req.ContactId,
        "member_id": memberId,
    }).Delete()
    if err != nil {
        return
    }
    
    // 添加新关系
    for _, groupId := range req.GroupIds {
        _, err = dao.ContactGroupRelation.Ctx(ctx).Insert(g.Map{
            "contact_id": req.ContactId,
            "group_id": groupId,
            "member_id": memberId,
            "created_by": memberId,
            "created_at": gtime.Now(),
        })
        if err != nil {
            return
        }
    }
    
    return &contact.UpdateGroupsRes{}, nil
}
```

## 🎨 最终效果

```
┌─────────────────────────────────────────────┐
│              通讯录管理                      │
├─────────────┬───────────────────────────────┤
│ 📁 所有联系人(50) │   [生成的联系人管理页面]      │
│ 📁 未分组(10)     │                         │
│ 📁 朋友(15)       │   - 搜索、筛选功能        │
│ 📁 同事(20)       │   - 新增、编辑、删除      │
│ 📁 客户(5)        │   - 导入、导出功能        │
│   └─ 重要客户(3)   │   - 完整的分页表格        │
└─────────────┴───────────────────────────────┘
```

### 多分组功能特点
- **联系人可以属于多个分组**：张三可以同时在"朋友"和"同事"分组中
- **分组统计独立计算**：每个分组显示的联系人数量是该分组下的实际联系人数（可能重复计算）
- **筛选功能**：点击分组时显示该分组下的所有联系人
- **编辑界面**：支持复选框多选分组，可以将联系人添加到多个分组
- **标签显示**：联系人列表中可以显示该联系人所属的所有分组标签

## 💡 方案优势

1. **充分利用代码生成器**：联系人管理功能完全自动生成
2. **支持多分组关系**：一个联系人可以属于多个分组，分类更灵活
3. **规范的数据结构**：采用标准的多对多关系设计，数据一致性好
4. **易于维护**：主要逻辑由代码生成器生成，定制部分很少
5. **扩展性好**：可以后续添加更多自定义功能

## 📋 注意事项

1. **权限控制**：确保 `member_id` 正确设置和验证
2. **分组统计**：联系人数量实时计算，确保数据准确性
3. **UI集成**：需要手动调整前端页面布局
4. **编辑界面**：需要修改生成的表单，支持多分组选择

通过这种方案，您既能享受代码生成器的便利，又能实现所需的界面效果！ 