# 短信报表按月统计查询修复

## 问题描述

在实现短信报表按月统计功能时，遇到了两个关键问题：

### 1. SQL字段冲突错误
**错误信息**：
```
Error 1052 (23000): Column 'created_at' in field list is ambiguous
```

**原因分析**：
在按月聚合查询中，由于使用了LEFT JOIN连接两个表（`hg_dx_sms_send_daily_stats` 和 `hg_admin_member`），两个表中都存在 `created_at` 字段，导致SQL查询时字段名冲突。

### 2. 前端日期格式错误
**错误信息**：
```
RangeError: Use `yyyy` instead of `YYYY` (in `YYYY-MM`) for formatting years
```

**原因分析**：
date-fns 库在新版本中要求使用 `yyyy` 格式而不是 `YYYY` 来格式化年份。

## 解决方案

### 1. 后端SQL字段冲突修复

#### 修改文件：`server/internal/logic/sys/dx_sms_send_daily_stats.go`

**问题代码**：
```go
selectFields := []string{
    "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m-01') as stat_date",
    dao.DxSmsSendDailyStats.Columns().MemberId + " as member_id",
    // ... 其他字段
    "MAX(" + dao.DxSmsSendDailyStats.Columns().CreatedAt + ") as created_at", // 冲突字段
}

groupFields := []string{
    "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m')", // 冲突字段
    dao.DxSmsSendDailyStats.Columns().MemberId, // 冲突字段
    dao.DxSmsSendDailyStats.Columns().SmsType,  // 冲突字段
}
```

**修复代码**：
```go
// 按月聚合的字段选择，需要明确指定表名避免字段冲突
selectFields := []string{
    "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m-01') as stat_date",
    dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().MemberId + " as member_id",
    dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().SmsType + " as sms_type",
    "SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().TotalCount + ") as total_count",
    "SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().SuccessCount + ") as success_count",
    "SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().FeeCount + ") as fee_count",
    "SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().FailCount + ") as fail_count",
    "SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().UnknownCount + ") as unknown_count",
    "CASE WHEN SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().TotalCount + ") > 0 THEN SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().SuccessCount + ") / SUM(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().TotalCount + ") * 100 ELSE 0 END as success_rate",
    "MAX(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().CreatedAt + ") as created_at",
}

// 添加用户相关字段
selectFields = append(selectFields, 
    dao.AdminMember.Table() + "." + dao.AdminMember.Columns().RealName+" as admin_member_real_name",
    dao.AdminMember.Table() + "." + dao.AdminMember.Columns().Username+" as admin_member_username",
)

// 按月分组，需要明确指定表名
groupFields := []string{
    "DATE_FORMAT(" + dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().StatDate + ", '%Y-%m')",
    dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().MemberId,
    dao.DxSmsSendDailyStats.Table() + "." + dao.DxSmsSendDailyStats.Columns().SmsType,
}
```

### 2. 前端日期格式修复

#### 修改文件：`web/src/views/dxSmsSendDailyStats/model.ts`

**问题代码**：
```typescript
render(row: State) {
  if (row.statDate && row.statDate.includes('-01 00:00:00')) {
    return formatToDate(row.statDate, 'YYYY-MM'); // 错误格式
  }
  return formatToDate(row.statDate);
}
```

**修复代码**：
```typescript
render(row: State) {
  if (row.statDate && row.statDate.includes('-01 00:00:00')) {
    return formatToDate(row.statDate, 'yyyy-MM'); // 正确格式
  }
  return formatToDate(row.statDate);
}
```

## 修复原理

### 1. SQL字段冲突解决原理
在多表JOIN查询中，当多个表包含相同名称的字段时，必须使用 `表名.字段名` 的格式来明确指定字段来源，避免数据库引擎无法确定字段归属。

**修复前的SQL**：
```sql
SELECT created_at, ... FROM table1 LEFT JOIN table2 ON ...
-- 数据库不知道created_at来自哪个表
```

**修复后的SQL**：
```sql
SELECT table1.created_at, ... FROM table1 LEFT JOIN table2 ON ...
-- 明确指定created_at来自table1
```

### 2. 日期格式标准化
date-fns 库遵循 Unicode Technical Standard #35，其中：
- `YYYY` 用于 ISO week-numbering year（ISO周年）
- `yyyy` 用于 calendar year（日历年）

对于常规的年月日格式化，应该使用 `yyyy`。

## 测试验证

### 1. 后端测试
- ✅ 编译通过
- ✅ SQL查询不再报字段冲突错误
- ✅ 按月聚合数据正确返回

### 2. 前端测试
- ✅ 日期格式化不再报错
- ✅ 按月数据正确显示为 "yyyy-MM" 格式
- ✅ 按天数据正常显示完整日期

## 技术要点总结

### 1. 多表JOIN查询最佳实践
- 始终使用表名前缀来限定字段名
- 特别注意聚合函数中的字段引用
- GROUP BY 子句中的字段也需要表名前缀

### 2. 日期格式化最佳实践
- 使用符合 Unicode 标准的格式化字符串
- `yyyy` 用于年份，`MM` 用于月份，`dd` 用于日期
- 避免使用已废弃的格式化字符

### 3. 错误排查技巧
- 查看完整的错误堆栈信息
- 分析SQL查询语句找出具体问题
- 关注第三方库的版本更新和API变化

## 后续优化建议

1. **代码规范**：建立统一的SQL字段引用规范，始终使用表名前缀
2. **测试覆盖**：增加多表JOIN查询的单元测试
3. **文档更新**：更新开发文档，说明日期格式化的标准用法
4. **依赖管理**：定期检查第三方库的更新和破坏性变更
